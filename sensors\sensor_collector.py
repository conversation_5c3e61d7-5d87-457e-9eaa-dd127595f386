import time
import serial
import threading
import json
import os
import random
import fcntl  # 添加文件锁支持
from datetime import datetime
from utils.config_loader import load_config
from utils.logger import get_logger
from sensors.cth import CO2TempHumiditySensor
from sensors.gps import GPSBDSModule
from sensors.ws import WindSpeedSensor

class SensorDataCollector:
    # 传感器状态常量
    STATUS_NORMAL = "正常"
    STATUS_DISCONNECTED = "断开连接"
    STATUS_RECONNECTING = "重新连接中"
    STATUS_TEMP_FAILURE = "临时故障"
    STATUS_PERM_FAILURE = "永久故障"
    
    def __init__(self, config=None):
        # 初始化日志
        self.logger = get_logger("sensor_collector")
        self.logger.info("初始化传感器收集器...")

        # 加载配置
        self.config = config if config else load_config()
        if not self.config:
            self.logger.error("无法加载配置文件")
            raise ValueError("无法加载配置文件")

        # 检查是否为测试模式
        self.test_mode = self.config.get('test_mode', {}).get('enabled', False)
        self.mock_hardware = self.config.get('test_mode', {}).get('mock_hardware', False)

        if self.test_mode:
            self.logger.info("🧪 传感器收集器运行在测试模式")
            if self.mock_hardware:
                self.logger.info("🧪 启用模拟硬件组件")
        
        self.serial_config = self.config['serial']
        self.logger.info(f"串口配置: 端口={self.serial_config['port']}, 波特率={self.serial_config['baudrate']}")
        
        self.serial_port = None
        self.collect_thread = None
        self.running = False
        
        # 添加文件操作锁，避免并发写入问题
        self.file_operation_lock = threading.RLock()
        
        # 内存缓存配置
        cache_config = self.config.get('cache', {})
        self.max_sensor_cache_size = cache_config.get('max_sensor_cache_size', 1000)
        self.max_error_cache_size = cache_config.get('max_error_cache_size', 500)
        self.cache_max_size = cache_config.get('cache_max_size', 50)  # 保持向后兼容

        # 添加内存缓存
        self.sensor_data_cache = []  # 传感器数据缓存
        self.sensor_errors_cache = []  # 错误数据缓存
        self.last_flush_time = time.time()  # 上次刷新时间
        self.flush_interval = cache_config.get('flush_interval', 5)  # 刷新间隔(秒)
        self.flush_thread = None  # 缓存刷新线程
        self.flush_event = threading.Event()  # 控制刷新线程的事件
        
        # 数据处理计数器 - 仅关注接口注入数据
        self.api_data_processed_count = 0  # 通过API接口注入的数据总量（包括正常和异常数据）
        self.api_data_error_count = 0      # 通过API接口注入的错误数据量
        self.api_data_normal_count = 0     # 通过API接口注入的正常数据量
        self.last_count_reset_time = time.time()  # 最后计数器重置时间
        
        # 传感器状态管理
        self.sensor_states = {
            'cth': {
                'status': self.STATUS_DISCONNECTED,
                'last_success': None,
                'retry_count': 0,
                'last_retry': None,
                'error_message': None
            },
            'gps': {
                'status': self.STATUS_DISCONNECTED,
                'last_success': None,
                'retry_count': 0,
                'last_retry': None,
                'error_message': None
            },
            'ws': {
                'status': self.STATUS_DISCONNECTED,
                'last_success': None,
                'retry_count': 0,
                'last_retry': None,
                'error_message': None
            }
        }
        
        # 传感器启用状态
        self.sensor_enabled = {
            'co2': True,  # 二氧化碳传感器
            'gps': True,  # GPS定位
            'hum': True,  # 湿度传感器
            'temp': True, # 温度传感器
            'ws': True    # 风速传感器
        }
        
        # 获取项目根目录
        self.root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # 确保数据目录存在（测试模式使用独立目录）
        data_dir_name = self.config['data_storage']['data_dir']
        self.data_dir = os.path.join(self.root_dir, data_dir_name, 'sensors')
        self.current_dir = os.path.join(self.data_dir, 'current')
        self.history_dir = os.path.join(self.data_dir, 'history')

        if self.test_mode:
            self.logger.info(f"🧪 测试模式数据目录: {self.data_dir}")
        
        for directory in [self.data_dir, self.current_dir, self.history_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.logger.info(f"创建目录: {directory}")
        
        # 数据文件路径
        self.sensor_data_file = os.path.join(self.current_dir, 'sensor_data.json')
        self.sensor_errors_file = os.path.join(self.current_dir, 'sensor_errors.json')
        self.sensor_data_archive_file = os.path.join(self.history_dir, 'sensor_data_archive.json')
        self.sensor_errors_archive_file = os.path.join(self.history_dir, 'sensor_errors_archive.json')
        
        # 初始化文件
        self._ensure_files_exist()
        
        # 检查运行环境
        self._check_environment()
        
        # 初始化模拟硬件控制器（测试模式）
        self.mock_controller = None
        if self.test_mode and self.mock_hardware:
            try:
                from testing_interface.mocks.mock_hardware import MockSensorController
                self.mock_controller = MockSensorController(self.config)
                self.logger.info("🧪 模拟硬件控制器初始化成功")
            except ImportError as e:
                self.logger.error(f"无法导入模拟硬件组件: {e}")
                self.mock_hardware = False

        # 串口初始化（非测试模式或非模拟硬件模式）
        if not (self.test_mode and self.mock_hardware):
            try:
                self.initialize_serial()
                self.logger.info("串口初始化成功")
            except Exception as e:
                self.logger.error(f"串口初始化失败: {e}", exc_info=True)
                # 不抛出异常，让数据收集线程后续尝试重连
        else:
            self.logger.info("🧪 测试模式：跳过真实串口初始化")
        
        # 设置最后备份时间
        self.last_backup_time = time.time()

        # 注册内存清理回调
        self._register_memory_callbacks()

        # 初始化异步文件I/O
        self._init_async_file_io()

        # 初始化线程管理器
        self._init_thread_manager()
    
    def _register_memory_callbacks(self):
        """注册内存监控回调"""
        try:
            from utils.memory_monitor import get_memory_monitor
            memory_monitor = get_memory_monitor()

            # 注册内存清理回调
            memory_monitor.add_cleanup_callback(self._emergency_cache_cleanup)
            memory_monitor.add_warning_callback(self._memory_warning_callback)

            self.logger.info("已注册内存监控回调")
        except Exception as e:
            self.logger.warning(f"注册内存监控回调失败: {e}")

    def _memory_warning_callback(self, memory_info):
        """内存告警回调"""
        cache_size = len(self.sensor_data_cache) + len(self.sensor_errors_cache)
        self.logger.warning(f"内存告警 - 当前缓存大小: 传感器数据={len(self.sensor_data_cache)}, 错误数据={len(self.sensor_errors_cache)}")

        # 如果缓存过大，主动刷新到磁盘
        if cache_size > 100:
            self._flush_cache_to_disk()

    def _emergency_cache_cleanup(self):
        """紧急内存清理回调"""
        with self.file_operation_lock:
            original_sensor_size = len(self.sensor_data_cache)
            original_error_size = len(self.sensor_errors_cache)

            # 强制刷新缓存到磁盘
            self._flush_cache_to_disk()

            # 如果缓存仍然很大，强制清理一半
            if len(self.sensor_data_cache) > 500:
                keep_size = len(self.sensor_data_cache) // 2
                self.sensor_data_cache = self.sensor_data_cache[-keep_size:]
                self.logger.warning(f"紧急清理传感器缓存: {original_sensor_size} -> {len(self.sensor_data_cache)}")

            if len(self.sensor_errors_cache) > 250:
                keep_size = len(self.sensor_errors_cache) // 2
                self.sensor_errors_cache = self.sensor_errors_cache[-keep_size:]
                self.logger.warning(f"紧急清理错误缓存: {original_error_size} -> {len(self.sensor_errors_cache)}")

    def _init_async_file_io(self):
        """初始化异步文件I/O"""
        try:
            from utils.async_file_io import get_async_file_io

            # 从配置中获取异步I/O配置
            async_io_config = self.config.get('async_file_io', {})

            # 获取异步I/O实例
            self.async_file_io = get_async_file_io(async_io_config)

            # 启动异步I/O（如果还未启动）
            if not self.async_file_io.running:
                self.async_file_io.start()

            self.logger.info("异步文件I/O初始化完成")

        except Exception as e:
            self.logger.warning(f"异步文件I/O初始化失败，将使用同步I/O: {e}")
            self.async_file_io = None

    def _init_thread_manager(self):
        """初始化线程管理器"""
        try:
            from utils.thread_manager import get_thread_manager

            # 获取线程管理器实例
            self.thread_manager = get_thread_manager()

            self.logger.info("线程管理器初始化完成")

        except Exception as e:
            self.logger.warning(f"线程管理器初始化失败，将使用传统线程: {e}")
            self.thread_manager = None

    def _ensure_files_exist(self):
        """确保数据文件存在"""
        for file_path in [self.sensor_data_file, self.sensor_errors_file, 
                          self.sensor_data_archive_file, self.sensor_errors_archive_file]:
            directory = os.path.dirname(file_path)
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                self.logger.info(f"创建目录: {directory}")
                
            if not os.path.exists(file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump([], f)
                self.logger.info(f"创建文件: {file_path}")
    
    # 安全读取JSON文件
    def read_json_safe(self, file_path):
        """安全读取JSON文件，处理可能的错误"""
        with self.file_operation_lock:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    fcntl.flock(f, fcntl.LOCK_SH)  # 获取共享锁
                    try:
                        content = f.read()
                        if not content.strip():
                            self.logger.info(f"文件为空: {file_path}")
                            return []
                        return json.loads(content)
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"JSON解析错误: {e}, 尝试修复...")
                        return self._repair_json_data(content, file_path)
                    finally:
                        fcntl.flock(f, fcntl.LOCK_UN)  # 释放锁
            except FileNotFoundError:
                self.logger.warning(f"文件不存在: {file_path}, 将创建新文件")
                self._ensure_files_exist()  # 确保文件存在
                return []
    
    # JSON数据修复函数
    def _repair_json_data(self, content, file_path):
        """尝试修复损坏的JSON数据"""
        try:
            # 创建备份
            backup_file = f"{file_path}.bak"
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            self.logger.info(f"已备份原始数据到: {backup_file}")
            
            # 基本修复尝试
            if content.strip().startswith('[') and not content.strip().endswith(']'):
                content = content.rstrip() + ']'
                self.logger.info("添加缺失的结束括号 ']'")
            
            # 尝试解析修复后的内容
            data = json.loads(content)
            self.logger.info("JSON数据修复成功")
            return data
        except Exception as e:
            self.logger.error(f"无法修复JSON数据: {e}，使用空数组")
            return []
    
    # 安全写入JSON文件
    def write_json_safe(self, file_path, data):
        """安全写入JSON文件，使用临时文件和文件锁"""
        with self.file_operation_lock:
            return self._write_json_safe_internal(file_path, data)
    
    def backup_sensor_data(self, force=False):
        """定期备份传感器数据
        
        Args:
            force: 是否强制备份，默认为False
        """
        current_time = time.time()
        # 每12小时备份一次，或强制备份
        if force or (current_time - self.last_backup_time > 12 * 3600):
            try:
                timestamp = time.strftime('%Y%m%d%H%M%S')
                backup_file = f"{self.sensor_data_file}.{timestamp}.bak"
                
                # 读取当前数据
                data = self.read_json_safe(self.sensor_data_file)
                
                # 写入备份文件
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2)
                    
                self.logger.info(f"已创建数据备份: {backup_file}")
                self.last_backup_time = current_time
            except Exception as e:
                self.logger.error(f"创建数据备份失败: {e}")
    
    def start(self):
        """启动传感器数据采集线程"""
        self.running = True

        # 重新获取线程管理器（确保获取到已启动的实例）
        self._init_thread_manager()

        # 优先使用线程管理器
        if self.thread_manager:
            # 使用线程管理器提交任务
            from utils.thread_manager import TaskPriority

            # 提交缓存刷新任务（定期任务）
            self.thread_manager.submit_periodic_task(
                name="sensor_cache_flush",
                func=self._flush_cache_once,
                interval=50  # 50秒刷新一次
            )

            # 提交数据采集任务
            self.collect_future = self.thread_manager.submit_task(
                name="sensor_data_collection",
                func=self.collection_loop,
                priority=TaskPriority.HIGH,
                max_retries=5
            )

            self.logger.info("传感器数据采集任务已提交到线程管理器")
        else:
            # 回退到传统线程方式
            self.flush_event.clear()
            self.flush_thread = threading.Thread(target=self._flush_cache_thread, daemon=True)
            self.flush_thread.start()
            self.logger.info("缓存刷新线程已启动")

            # 启动数据采集线程
            self.collect_thread = threading.Thread(target=self.collection_loop, daemon=True)
            self.collect_thread.start()
            self.logger.info("传感器数据采集线程已启动")
        
        # 通知MQTT客户端传感器收集器已就绪
        try:
            import builtins
            if hasattr(builtins, 'mqtt_client_instance'):
                mqtt_client = getattr(builtins, 'mqtt_client_instance')
                mqtt_client.set_component_ready("sensor_collector", True)
        except Exception as e:
            self.logger.error(f"通知MQTT客户端传感器收集器就绪状态失败: {e}")
        
    def stop(self):
        """停止传感器数据采集线程"""
        self.logger.info("\n正在停止传感器数据采集线程...")
        
        # 停止数据采集线程
        self.running = False

        # 处理线程管理器模式
        if hasattr(self, 'collect_future') and self.collect_future:
            try:
                self.collect_future.cancel()
                self.logger.info("已取消传感器数据采集任务")
            except Exception as e:
                self.logger.warning(f"取消传感器数据采集任务失败: {e}")
        elif hasattr(self, 'collect_thread') and self.collect_thread:
            self.collect_thread.join(timeout=2.0)

        # 停止缓存刷新线程
        self.flush_event.set()
        if hasattr(self, 'flush_thread') and self.flush_thread:
            self.flush_thread.join(timeout=2.0)

        # 取消定期任务
        if self.thread_manager:
            try:
                self.thread_manager.cancel_periodic_task("sensor_cache_flush")
                self.logger.info("已取消传感器缓存刷新定期任务")
            except Exception as e:
                self.logger.warning(f"取消定期任务失败: {e}")
            
        # 在停止前强制刷新缓存
        try:
            self.logger.info("执行停止前的缓存刷新...")
            self._flush_cache_to_disk()
        except Exception as e:
            self.logger.error(f"停止前刷新缓存失败: {e}")
            
        # 在停止前执行强制备份
        try:
            self.logger.info("执行停止前的数据强制备份...")
            self.backup_sensor_data(force=True)
        except Exception as e:
            self.logger.error(f"停止前备份数据失败: {e}")

        # 停止模拟硬件控制器
        if self.mock_controller:
            try:
                self.mock_controller.stop()
                self.logger.info("🧪 模拟硬件控制器已停止")
            except Exception as e:
                self.logger.error(f"停止模拟硬件控制器失败: {e}")

        self.close()
        self.logger.info("传感器数据采集线程已停止")
        
        # 通知MQTT客户端传感器收集器已停止
        try:
            import builtins
            if hasattr(builtins, 'mqtt_client_instance'):
                mqtt_client = getattr(builtins, 'mqtt_client_instance')
                mqtt_client.set_component_ready("sensor_collector", False)
        except Exception as e:
            self.logger.error(f"通知MQTT客户端传感器收集器停止状态失败: {e}")
    
    def close(self):
        """关闭串口连接"""
        if self.serial_port and self.serial_port.is_open:
            try:
                self.serial_port.close()
                self.logger.info("串口连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭串口连接时发生错误: {e}")
        
    def collection_loop(self):
        """数据采集循环，每分钟采集一次传感器数据并保存"""
        self.logger.info(f"[采集线程] 开始传感器数据采集循环(采集间隔:60秒), 线程ID={threading.get_ident()}")
        # self.last_report_time = 0
        # self.first_report_done = False
        # self.report_interval = 600  # 10分钟
        while self.running:
            try:
                self.logger.info("--- 开始传感器数据采集周期 ---")
                # 采集数据
                data = self.collect_data()
                # 保存到本地文件
                if data and len(data) > 2:
                    self.save_sensor_data(data)
                else:
                    self.logger.warning("未采集到有效数据，本次采集周期跳过保存")
                # 定期备份检查
                self.backup_sensor_data(force=False)

                # 更新线程活动时间，防止被误判为无响应
                if self.thread_manager:
                    self.thread_manager.update_thread_activity()

                # 分段睡眠，便于及时响应停止信号
                for i in range(60):
                    if not self.running:
                        break
                    # 每10秒更新一次线程活动时间
                    if i % 10 == 0 and self.thread_manager:
                        self.thread_manager.update_thread_activity()
                    time.sleep(1)
            except Exception as e:
                self.logger.error(f"数据采集循环发生错误: {e}", exc_info=True)
                for i in range(10):
                    if not self.running:
                        break
                    time.sleep(1)
        
    def initialize_serial(self):
        """初始化串口"""
        try:
            self.logger.debug(f"尝试打开串口: {self.serial_config['port']}")
            self.serial_port = serial.Serial(
                port=self.serial_config['port'],
                baudrate=self.serial_config['baudrate'],
                timeout=self.serial_config['timeout']
            )
            if not self.serial_port.is_open:
                self.serial_port.open()
            time.sleep(0.1)  # 等待串口初始化
            self.logger.info(f"串口 {self.serial_config['port']} 打开成功")
        except Exception as e:
            self.logger.error(f"串口初始化失败: {e}", exc_info=True)
            raise
    
    def collect_data(self):
        """
        采集所有传感器数据
        
        Returns:
            包含所有传感器数据的字典
        """
        data = {
            'timestamp': int(time.time()),  # 修改为秒级整形时间戳
            'datetime': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.logger.info(f"开始采集传感器数据: {data['datetime']}")
        
        # 检查串口是否可用（测试模式下跳过串口操作）
        if not (self.test_mode and self.mock_hardware):
            if not self.serial_port or not self.serial_port.is_open:
                self.logger.warning("串口未连接，尝试重新连接")
                try:
                    self.initialize_serial()
                    self.logger.info("串口重新连接成功")
                except Exception as e:
                    self.logger.error(f"串口重新连接失败: {e}")
                    self._record_error("all", "串口连接失败", str(e), trigger_check=True)
                    return data
        else:
            self.logger.debug("🧪 测试模式：跳过串口连接检查")
        
        # 采集温湿度CO2传感器数据
        if self.sensor_enabled['temp'] or self.sensor_enabled['hum'] or self.sensor_enabled['co2']:
            self.logger.info("开始采集温湿度CO2数据...")
            cth_data = self.get_cth_data()
            if cth_data:
                # 根据启用状态过滤数据
                filtered_cth_data = {}
                if self.sensor_enabled['temp'] and 'temperature' in cth_data:
                    filtered_cth_data['temperature'] = cth_data['temperature']
                if self.sensor_enabled['hum'] and 'humidity' in cth_data:
                    filtered_cth_data['humidity'] = cth_data['humidity']
                if self.sensor_enabled['co2'] and 'co2' in cth_data:
                    filtered_cth_data['co2'] = cth_data['co2']
                
                data.update(filtered_cth_data)
                self.logger.info("温湿度CO2数据采集成功")
                self._update_sensor_state('cth', self.STATUS_NORMAL)
            else:
                self.logger.warning("温湿度CO2数据采集失败")
                self._handle_sensor_failure('cth', "无法读取温湿度CO2数据")
        else:
            self.logger.info("温湿度CO2传感器已禁用，跳过数据采集")
        
        # 采集GPS数据
        if self.sensor_enabled['gps']:
            self.logger.info("开始采集GPS数据...")
            gps_data = self.get_gps_data()
            if gps_data:
                data.update(gps_data)
                self.logger.info("GPS数据采集成功")
                self._update_sensor_state('gps', self.STATUS_NORMAL)
            else:
                self.logger.warning("GPS数据采集失败")
                self._handle_sensor_failure('gps', "无法读取GPS数据")
        else:
            self.logger.info("GPS传感器已禁用，跳过数据采集")
        
        # 采集风速数据
        if self.sensor_enabled['ws']:
            self.logger.info("开始采集风速数据...")
            wind_data = self.get_wind_speed()
            if wind_data:
                data.update(wind_data)
                self.logger.info("风速数据采集成功")
                self._update_sensor_state('ws', self.STATUS_NORMAL)
            else:
                self.logger.warning("风速数据采集失败")
                self._handle_sensor_failure('ws', "无法读取风速数据")
        else:
            self.logger.info("风速传感器已禁用，跳过数据采集")
            
        # 检查是否有有效数据
        valid_keys = [key for key in data.keys() if key not in ['timestamp', 'datetime']]
        if valid_keys:
            self.logger.info(f"成功采集到 {len(valid_keys)} 项传感器数据")
            self.logger.debug(f"传感器数据: {', '.join([f'{k}={data[k]}' for k in valid_keys])}")
            
            # 数据质量验证
            data, _ = self.verify_data_quality(data)
            self.logger.info("完成数据质量验证")
        else:
            self.logger.warning("未采集到任何有效传感器数据")
        
        return data
    
    def _update_sensor_state(self, sensor_type, status, error_msg=None):
        """
        更新传感器状态，只在状态变化时触发相应动作

        Args:
            sensor_type: 传感器类型 (cth, gps, ws)
            status: 状态值
            error_msg: 错误信息
        """
        if sensor_type not in self.sensor_states:
            self.logger.error(f"未知传感器类型: {sensor_type}")
            return

        state = self.sensor_states[sensor_type]
        old_status = state['status']
        old_error_msg = state.get('error_message', '')

        # 更新状态
        state['status'] = status
        state['last_update'] = int(time.time())

        # 只在状态发生变化时触发相应动作
        if old_status != status:
            self.logger.info(f"{sensor_type}传感器状态变化: {old_status} → {status}")

            if status == self.STATUS_NORMAL:
                # 如果传感器恢复正常，重置错误计数
                state['last_success'] = int(time.time())
                state['retry_count'] = 0
                state['error_message'] = None

                # 从异常恢复到正常 → 立即上报恢复
                if old_status in [self.STATUS_TEMP_FAILURE, self.STATUS_PERM_FAILURE, self.STATUS_RECONNECTING]:
                    self.logger.info(f"{sensor_type}传感器已恢复正常，立即上报恢复状态")
                    self._report_sensor_recovery(sensor_type)

            elif status == self.STATUS_PERM_FAILURE:
                # 记录错误信息
                if error_msg:
                    state['error_message'] = error_msg

                # 首次进入永久故障 → 立即上报
                self.logger.error(f"{sensor_type}传感器首次进入永久故障: {error_msg}")
                self._record_error(sensor_type, "传感器永久故障", error_msg or "未知错误", trigger_check=True)
                self._report_sensor_failure_to_device_check(sensor_type, error_msg)

            elif status in [self.STATUS_TEMP_FAILURE, self.STATUS_RECONNECTING]:
                # 记录错误信息
                if error_msg:
                    state['error_message'] = error_msg

                # 临时故障和重连中状态不触发自检，只记录错误
                self.logger.warning(f"{sensor_type}传感器进入{status}状态: {error_msg}")
                self._record_error(sensor_type, status, error_msg or "未知错误", trigger_check=False)
        else:
            # 状态未变化，检查是否需要使用退避策略
            if status in [self.STATUS_TEMP_FAILURE, self.STATUS_PERM_FAILURE, self.STATUS_RECONNECTING]:
                # 相同异常状态重复，使用退避策略记录
                self._record_error_with_backoff(sensor_type, status, error_msg or "未知错误")
        
    def _handle_sensor_failure(self, sensor_type, error_msg):
        """
        处理传感器失败情况，实现指数退避重连策略
        
        Args:
            sensor_type: 传感器类型
            error_msg: 错误信息
        """
        if sensor_type not in self.sensor_states:
            self.logger.error(f"未知传感器类型: {sensor_type}")
            return
            
        state = self.sensor_states[sensor_type]
        current_time = int(time.time())  # 修改为秒级整形时间戳
        
        # 增加重试计数
        state['retry_count'] += 1
        state['last_retry'] = current_time
        state['error_message'] = error_msg
        
        # 计算指数退避时间间隔
        # 从配置文件读取基础重试间隔和最大间隔
        base_interval = self.config['retry']['base_interval']  # 基础重试间隔（秒）
        max_interval = self.config['retry']['max_interval']    # 最大重试间隔（秒）
        
        # 从配置文件读取临时故障和永久故障的重试次数阈值
        temp_failure_count = self.config['retry']['temp_failure_count']
        perm_failure_count = self.config['retry']['perm_failure_count']
        
        # 计算重试间隔：永久故障状态下使用固定间隔，其他状态使用指数退避
        if state['retry_count'] >= perm_failure_count:
            retry_interval = 60  # 永久故障状态下每分钟尝试一次
        else:
            retry_interval = min(base_interval * (2 ** (state['retry_count'] - 1)), max_interval)
        
        # 记录日志
        next_retry_time = datetime.fromtimestamp(current_time + retry_interval)
        
        if state['retry_count'] < temp_failure_count:
            # 前几次故障视为临时故障
            self._update_sensor_state(sensor_type, self.STATUS_TEMP_FAILURE, error_msg)
            self.logger.warning(
                f"{sensor_type}传感器出现临时故障: {error_msg}, "
                f"重试计数: {state['retry_count']}, "
                f"下次重试时间: {next_retry_time.strftime('%H:%M:%S')}"
            )

        elif state['retry_count'] < perm_failure_count:
            # 多次失败改为重连状态
            self._update_sensor_state(sensor_type, self.STATUS_RECONNECTING, error_msg)
            self.logger.warning(
                f"{sensor_type}传感器重连中: {error_msg}, "
                f"重试计数: {state['retry_count']}, "
                f"下次重试时间: {next_retry_time.strftime('%H:%M:%S')}"
            )

        else:
            # 大量失败视为永久故障，但仍然会继续尝试恢复
            self._update_sensor_state(sensor_type, self.STATUS_PERM_FAILURE, error_msg)
            self.logger.error(
                f"{sensor_type}传感器出现永久故障: {error_msg}, "
                f"重试计数: {state['retry_count']}, "
                f"但仍将每{retry_interval}秒尝试恢复"
            )
        
        # 安排下次重连，即使是永久故障也会尝试恢复
        self._schedule_reconnect(sensor_type, retry_interval)
    
    def _try_reconnect_sensor(self, sensor_id):
        """尝试重新连接传感器"""
        self._update_sensor_state(sensor_id, self.STATUS_RECONNECTING)
        self.logger.info(f"尝试重新连接 {sensor_id} 传感器")
        
        # 实际重连逻辑在相应的get_xxx_data方法中
        # 这里只是更新状态并记录重试时间
        self.sensor_states[sensor_id]['last_retry'] = int(time.time())
    
    def _schedule_reconnect(self, sensor_id, retry_interval):
        """安排下次重连时间
        
        Args:
            sensor_id: 传感器类型ID
            retry_interval: 重试间隔（秒）
        """
        # 使用指定的重试间隔
        next_retry = time.time() + retry_interval
        
        def reconnect_task():
            # 防止线程死锁
            if sensor_id not in self.sensor_states:
                return
                
            self.logger.info(f"执行 {sensor_id} 传感器重连尝试")
            if sensor_id == 'cth':
                self.get_cth_data(force_retry=True)
            elif sensor_id == 'gps':
                self.get_gps_data(force_retry=True)
            elif sensor_id == 'ws':
                self.get_wind_speed(force_retry=True)
        
        # 创建一个线程在指定时间后执行重连
        threading.Timer(retry_interval, reconnect_task).start()
        
        next_retry_time = datetime.fromtimestamp(next_retry)
        self.logger.info(f"已安排 {sensor_id} 传感器的下次重连时间: {next_retry_time.strftime('%H:%M:%S')}")
    
    def _record_error(self, sensor_type, error_type, error_msg, is_api_data=False, trigger_check=True):
        """记录传感器错误

        Args:
            sensor_type: 传感器类型
            error_type: 错误类型
            error_msg: 错误消息
            is_api_data: 是否为API注入的数据错误，默认为False
            trigger_check: 是否触发自检，默认为True（保持向后兼容）
        """
        try:
            # 只对API注入的数据进行错误计数
            if is_api_data:
                self.api_data_error_count += 1
            
            # 构造错误数据
            error_entry = {
                "time": int(time.time()),
                "datetime": time.strftime('%Y-%m-%d %H:%M:%S'),
                "sensor_type": sensor_type,
                "error_type": error_type,
                "error_message": error_msg,
                "uploaded": False
            }
            
            # 添加到内存缓存，而不是直接写入文件
            with self.file_operation_lock:
                self.sensor_errors_cache.append(error_entry)

                # 检查错误缓存大小限制，实现FIFO淘汰策略
                if len(self.sensor_errors_cache) > self.max_error_cache_size:
                    removed_count = len(self.sensor_errors_cache) - self.max_error_cache_size
                    self.sensor_errors_cache = self.sensor_errors_cache[-self.max_error_cache_size:]
                    self.logger.warning(f"错误数据缓存超限，已淘汰最旧的{removed_count}条数据，当前缓存大小: {len(self.sensor_errors_cache)}")
                
            # 检查是否需要立即刷新缓存
            if len(self.sensor_errors_cache) >= self.cache_max_size:
                # 使用单独线程执行刷新，避免阻塞当前线程
                threading.Thread(target=self._flush_cache_to_disk, daemon=True).start()
            
            self.logger.info(f"已记录传感器错误: {sensor_type} - {error_type}")
                
            # 尝试通过MQTT上传错误
            mqtt_upload_success = False
            try:
                import builtins
                if hasattr(builtins, 'mqtt_client_instance'):
                    mqtt_client = getattr(builtins, 'mqtt_client_instance')
                    # 同步计数到MQTT客户端
                    mqtt_client.update_sensor_counts(self.api_data_processed_count, self.api_data_normal_count, self.api_data_error_count)
                    
                    # 增加错误数据处理计数
                    mqtt_client.error_data_processed_count += 1
                    
                    # 只在需要触发自检时才通过MQTT上传错误数据（重定向到自检）
                    if trigger_check:
                        if mqtt_client.connected:
                            mqtt_client.publish_error_data(error_entry)
                            self.logger.info("已将错误数据发送到MQTT客户端（重定向到自检）")
                            mqtt_upload_success = True
                        else:
                            # MQTT未连接，将错误数据加入待处理队列
                            if not mqtt_client.error_data_queue.full():
                                mqtt_client.error_data_queue.put(error_entry, block=False)
                                self.logger.info(f"MQTT未连接，已将错误数据加入待处理队列: {error_entry['datetime']}")
                            else:
                                self.logger.warning("错误数据待处理队列已满，无法加入更多数据")
                    else:
                        self.logger.debug(f"错误记录不触发自检: {sensor_type} - {error_type}")
            except Exception as e:
                self.logger.error(f"通过MQTT上传错误数据失败: {e}")
                
        except Exception as e:
            self.logger.error(f"记录传感器错误失败: {e}", exc_info=True)

    def _record_error_with_backoff(self, sensor_type, error_type, error_msg):
        """使用退避策略记录错误，避免频繁重复上报相同状态

        Args:
            sensor_type: 传感器类型
            error_type: 错误类型
            error_msg: 错误消息
        """
        try:
            # 获取设备自检系统实例，使用其退避机制
            import builtins
            if hasattr(builtins, 'device_health_instance'):
                device_health = getattr(builtins, 'device_health_instance')

                # 映射传感器类型到组件名
                component_map = {
                    'cth': ['co2', 'temp', 'hum'],
                    'gps': ['gps'],
                    'ws': ['ws']
                }

                components = component_map.get(sensor_type, [sensor_type])
                for component in components:
                    # 使用设备自检系统的退避机制
                    device_health.report_component_status_with_backoff(
                        component,
                        device_health.STATUS_ERROR,
                        f"{sensor_type}传感器{error_type}: {error_msg}"
                    )
            else:
                # 如果设备自检系统不可用，仍然记录错误但不触发自检
                self._record_error(sensor_type, error_type, error_msg, trigger_check=False)

        except Exception as e:
            self.logger.error(f"使用退避策略记录错误失败: {e}", exc_info=True)
            # 降级到普通错误记录
            self._record_error(sensor_type, error_type, error_msg, trigger_check=False)

    def verify_data_quality(self, data, is_api_data=False):
        """
        验证传感器数据质量，异常数据用{"value": xxx, "anomaly": "xxx"}结构，正常数据为纯数值
        """
        if not data:
            return data, False
        validated_data = data.copy()
        data_modified = False
        last_valid_data = getattr(self, '_last_valid_data', {})
        # 温度
        if 'temperature' in data:
            temp = data['temperature']
            temp_min = self.config['sensors']['cth']['temperature_min']
            temp_max = self.config['sensors']['cth']['temperature_max']
            if temp < temp_min or temp > temp_max:
                validated_data['temperature'] = {"value": temp, "anomaly": "extreme"}
                data_modified = True
            elif 'temperature' in last_valid_data:
                temp_change = abs(temp - last_valid_data['temperature'])
                temp_change_threshold = self.config.get('thresholds', {}).get('sudden_change', {}).get('temp', 5.0)
                if temp_change > temp_change_threshold:
                    validated_data['temperature'] = {"value": temp, "anomaly": "sudden_change"}
                    data_modified = True
        # 湿度
        if 'humidity' in data:
            humidity = data['humidity']
            humidity_min = self.config['sensors']['cth']['humidity_min']
            humidity_max = self.config['sensors']['cth']['humidity_max']
            if humidity < humidity_min or humidity > humidity_max:
                validated_data['humidity'] = {"value": humidity, "anomaly": "extreme"}
                data_modified = True
            elif 'humidity' in last_valid_data:
                hum_change = abs(humidity - last_valid_data['humidity'])
                hum_change_threshold = self.config.get('thresholds', {}).get('sudden_change', {}).get('hum', 20.0)
                if hum_change > hum_change_threshold:
                    validated_data['humidity'] = {"value": humidity, "anomaly": "sudden_change"}
                    data_modified = True
        # CO2
        if 'co2' in data:
            co2 = data['co2']
            co2_min = self.config['sensors']['cth']['co2_min']
            co2_max = self.config['sensors']['cth']['co2_max']
            if co2 < co2_min or co2 > co2_max:
                validated_data['co2'] = {"value": co2, "anomaly": "extreme"}
                data_modified = True
            elif 'co2' in last_valid_data:
                co2_change = abs(co2 - last_valid_data['co2'])
                co2_change_threshold = self.config.get('thresholds', {}).get('sudden_change', {}).get('co2', 1000)
                if co2_change > co2_change_threshold:
                    validated_data['co2'] = {"value": co2, "anomaly": "sudden_change"}
                    data_modified = True
        # 风速
        if 'wind_speed' in data:
            wind_speed = data['wind_speed']
            ws_min = self.config['sensors']['wind_speed']['min']
            ws_max = self.config['sensors']['wind_speed']['max']
            if wind_speed < ws_min or wind_speed > ws_max:
                validated_data['wind_speed'] = {"value": wind_speed, "anomaly": "extreme"}
                data_modified = True
            elif 'wind_speed' in last_valid_data:
                ws_change = abs(wind_speed - last_valid_data['wind_speed'])
                ws_change_threshold = self.config.get('thresholds', {}).get('sudden_change', {}).get('ws', 10.0)
                if ws_change > ws_change_threshold:
                    validated_data['wind_speed'] = {"value": wind_speed, "anomaly": "sudden_change"}
                    data_modified = True
        # GPS
        if 'longitude' in data and 'latitude' in data:
            lon, lat = data['longitude'], data['latitude']
            # 保留6位小数
            try:
                lon = float(lon)
                lat = float(lat)
                validated_data['longitude'] = f"{lon:.6f}"
                validated_data['latitude'] = f"{lat:.6f}"
            except Exception:
                pass
        # 保存有效数据作为下次比较的基准
        if not data_modified:
            self._last_valid_data = {k: v for k, v in data.items() if k not in ['timestamp', 'datetime']}
        return validated_data, data_modified
    
    def save_sensor_data(self, data, is_api_data=False):
        """
        保存传感器数据到缓存，由缓存刷新线程统一写入文件
        """
        if not data:
            self.logger.warning("无数据可保存")
            return None
        try:
            data_valid, data_modified = self.verify_data_quality(data, is_api_data)
            # 统一使用time字段作为时间戳
            timestamp = data.get("time", data.get("timestamp", int(time.time())))

            # 检查是否已存在相同时间戳的数据，避免重复保存
            with self.file_operation_lock:
                existing_timestamps = {item.get('time') for item in self.sensor_data_cache}
                if timestamp in existing_timestamps:
                    self.logger.debug(f"跳过重复时间戳的数据: {timestamp}")
                    return timestamp

            # 获取设备ID
            device_id = self.config.get('device_binding', {}).get('device_id', '')

            data_entry = {
                "time": timestamp,
                "devid": device_id,
                "ver": "2.0",
                "dir": "up",
                "data": {},
                "uploaded": False
            }
            if "temperature" in data_valid and self.sensor_enabled['temp']:
                data_entry["data"]["temp"] = data_valid["temperature"]
            if "humidity" in data_valid and self.sensor_enabled['hum']:
                data_entry["data"]["hum"] = data_valid["humidity"]
            if "co2" in data_valid and self.sensor_enabled['co2']:
                data_entry["data"]["co2"] = data_valid["co2"]
            if "longitude" in data_valid and "latitude" in data_valid and self.sensor_enabled['gps']:
                data_entry["data"]["gps"] = f"{data_valid['longitude']},{data_valid['latitude']}"
            if "wind_speed" in data_valid and self.sensor_enabled['ws']:
                data_entry["data"]["ws"] = data_valid["wind_speed"]

            # 添加到内存缓存
            with self.file_operation_lock:
                # 修复：检查重复时间戳，使用time字段保持一致性
                existing_timestamps = [item.get('time') for item in self.sensor_data_cache]
                if timestamp in existing_timestamps:
                    self.logger.warning(f"跳过重复传感器数据保存: 时间戳={timestamp} 已存在")
                    return

                self.sensor_data_cache.append(data_entry)

                # 检查缓存大小限制，实现FIFO淘汰策略
                if len(self.sensor_data_cache) > self.max_sensor_cache_size:
                    removed_count = len(self.sensor_data_cache) - self.max_sensor_cache_size
                    self.sensor_data_cache = self.sensor_data_cache[-self.max_sensor_cache_size:]
                    self.logger.warning(f"传感器数据缓存超限，已淘汰最旧的{removed_count}条数据，当前缓存大小: {len(self.sensor_data_cache)}")

                self.logger.info(f"传感器数据已保存到缓存: 时间戳={timestamp}, 缓存大小: {len(self.sensor_data_cache)}/{self.max_sensor_cache_size}")

            # 检查是否需要立即刷新缓存
            if len(self.sensor_data_cache) >= self.cache_max_size:
                threading.Thread(target=self._flush_cache_to_disk, daemon=True).start()

            try:
                import builtins
                if hasattr(builtins, 'mqtt_client_instance'):
                    mqtt_client = getattr(builtins, 'mqtt_client_instance')
                    mqtt_client.update_sensor_counts(
                        self.api_data_processed_count,
                        self.api_data_normal_count,
                        self.api_data_error_count
                    )
            except Exception as e:
                self.logger.error(f"同步计数到MQTT客户端失败: {e}")
            return timestamp
        except Exception as e:
            self.logger.error(f"保存传感器数据失败: {e}", exc_info=True)
            return None
    
    def get_cth_data(self, force_retry=False):
        """获取温湿度CO2数据
        
        Args:
            force_retry: 是否强制重试，即使传感器处于故障状态
            
        Returns:
            dict: 包含温度、湿度、CO2数据的字典，失败时返回None
        """
        sensor_type = 'cth'
        
        # 检查传感器是否被禁用
        if not all(self.sensor_enabled.get(key, False) for key in ['co2', 'temp', 'hum']):
            self.logger.debug("温湿度CO2传感器已被禁用")
            return None
        
        # 检查传感器状态
        if not force_retry and self.sensor_states[sensor_type]['status'] == self.STATUS_PERM_FAILURE:
            self.logger.debug("温湿度CO2传感器处于永久故障状态，跳过读取")
            return None

        # 测试模式使用模拟数据
        if self.test_mode and self.mock_hardware and self.mock_controller:
            try:
                data = self.mock_controller.get_sensor_data('cth')
                if data:
                    self.logger.debug(f"🧪 模拟温湿度CO2数据: 温度={data['temperature']}℃, 湿度={data['humidity']}%, CO2={data['co2']}ppm")
                    self._update_sensor_state(sensor_type, self.STATUS_NORMAL)
                    return data
                else:
                    self._handle_sensor_failure(sensor_type, "模拟数据获取失败")
                    return None
            except Exception as e:
                self.logger.error(f"获取模拟温湿度CO2数据异常: {e}", exc_info=True)
                self._handle_sensor_failure(sensor_type, str(e))
                return None

        # 生产模式使用真实传感器
        try:
            cth_config = self.config['sensors']['cth']
            self.logger.debug(f"尝试读取温湿度CO2数据，设备地址: {cth_config['device_address']}")

            sensor = CO2TempHumiditySensor(self.serial_config['port'])
            data = sensor.read_sensor_data(cth_config['device_address'])
            
            if data:
                self.logger.info(f"读取温湿度CO2成功: 温度={data['temperature']}℃, 湿度={data['humidity']}%, CO2={data['co2']}ppm")
                self._update_sensor_state(sensor_type, self.STATUS_NORMAL)
                return data
            else:
                self.logger.warning("读取温湿度CO2失败: 返回数据为空")
                self._handle_sensor_failure(sensor_type, "返回数据为空")
                return None
        except Exception as e:
            self.logger.error(f"读取温湿度CO2数据异常: {e}", exc_info=True)
            self._handle_sensor_failure(sensor_type, str(e))
            return None

    def get_gps_data(self, force_retry=False):
        """获取GPS数据
        
        Args:
            force_retry: 是否强制重试，即使传感器处于故障状态
            
        Returns:
            dict: 包含经纬度的字典，失败时返回None
        """
        sensor_type = 'gps'
        
        # 检查传感器是否被禁用
        if not self.sensor_enabled.get('gps', False):
            self.logger.debug("GPS传感器已被禁用")
            return None
        
        # 检查传感器状态
        if not force_retry and self.sensor_states[sensor_type]['status'] == self.STATUS_PERM_FAILURE:
            self.logger.debug("GPS传感器处于永久故障状态，跳过读取")
            return None

        # 测试模式使用模拟数据
        if self.test_mode and self.mock_hardware and self.mock_controller:
            try:
                data = self.mock_controller.get_sensor_data('gps')
                if data:
                    self.logger.debug(f"🧪 模拟GPS数据: 经度={data['longitude']}, 纬度={data['latitude']}")
                    self._update_sensor_state(sensor_type, self.STATUS_NORMAL)
                    return {
                        'longitude': data['longitude'],
                        'latitude': data['latitude']
                    }
                else:
                    self._handle_sensor_failure(sensor_type, "模拟GPS数据获取失败")
                    return None
            except Exception as e:
                self.logger.error(f"获取模拟GPS数据异常: {e}", exc_info=True)
                self._handle_sensor_failure(sensor_type, str(e))
                return None

        # 生产模式使用真实传感器
        try:
            gps_config = self.config['sensors']['gps']
            self.logger.debug(f"尝试读取GPS数据，设备地址: {gps_config['device_address']}")

            sensor = GPSBDSModule(self.serial_config['port'])
            data = sensor.read_position(gps_config['device_address'])
            
            if data:
                self.logger.info(f"读取GPS成功: 经度={data['bd_longitude']}, 纬度={data['bd_latitude']}")
                self._update_sensor_state(sensor_type, self.STATUS_NORMAL)
                return {
                    'longitude': data['bd_longitude'],
                    'latitude': data['bd_latitude']
                }
            else:
                self.logger.warning("读取GPS失败: 返回数据为空")
                self._handle_sensor_failure(sensor_type, "返回数据为空")
                return None
        except Exception as e:
            self.logger.error(f"读取GPS数据异常: {e}", exc_info=True)
            self._handle_sensor_failure(sensor_type, str(e))
            return None

    def get_wind_speed(self, force_retry=False):
        """获取风速数据
        
        Args:
            force_retry: 是否强制重试，即使传感器处于故障状态
            
        Returns:
            dict: 包含风速的字典，失败时返回None
        """
        sensor_type = 'ws'
        
        # 检查传感器是否被禁用
        if not self.sensor_enabled.get('ws', False):
            self.logger.debug("风速传感器已被禁用")
            return None
        
        # 检查传感器状态
        if not force_retry and self.sensor_states[sensor_type]['status'] == self.STATUS_PERM_FAILURE:
            self.logger.debug("风速传感器处于永久故障状态，跳过读取")
            return None

        # 测试模式使用模拟数据
        if self.test_mode and self.mock_hardware and self.mock_controller:
            try:
                wind_speed = self.mock_controller.get_sensor_data('wind_speed')
                if wind_speed is not None:
                    self.logger.debug(f"🧪 模拟风速数据: {wind_speed} m/s")
                    self._update_sensor_state(sensor_type, self.STATUS_NORMAL)
                    return {'wind_speed': wind_speed}
                else:
                    self._handle_sensor_failure(sensor_type, "模拟风速数据获取失败")
                    return None
            except Exception as e:
                self.logger.error(f"获取模拟风速数据异常: {e}", exc_info=True)
                self._handle_sensor_failure(sensor_type, str(e))
                return None

        # 生产模式使用真实传感器
        try:
            ws_config = self.config['sensors']['wind_speed']
            self.logger.debug(f"尝试读取风速数据，设备地址: {ws_config['device_address']}")

            sensor = WindSpeedSensor(self.serial_config['port'])
            wind_speed = sensor.read_wind_speed(ws_config['device_address'])
            
            if wind_speed is not None:
                self.logger.info(f"读取风速成功: {wind_speed} m/s")
                self._update_sensor_state(sensor_type, self.STATUS_NORMAL)
                return {'wind_speed': wind_speed}
            else:
                self.logger.warning("读取风速失败: 返回数据为空")
                self._handle_sensor_failure(sensor_type, "返回数据为空")
                return None
        except Exception as e:
            self.logger.error(f"读取风速数据异常: {e}", exc_info=True)
            self._handle_sensor_failure(sensor_type, str(e))
            return None

    # 添加传感器启用和禁用方法
    def enable_sensor(self, sensor_type):
        """启用指定传感器
        
        Args:
            sensor_type: 传感器类型，可选值: co2, gps, hum, temp, ws
        
        Returns:
            bool: 操作是否成功
        """
        if sensor_type in self.sensor_enabled:
            if not self.sensor_enabled[sensor_type]:
                self.sensor_enabled[sensor_type] = True
                self.logger.info(f"已启用 {sensor_type} 传感器")
            else:
                self.logger.info(f"{sensor_type} 传感器已经是启用状态")
            return True
        else:
            self.logger.warning(f"未知传感器类型: {sensor_type}")
            return False
    
    def disable_sensor(self, sensor_type):
        """禁用指定传感器
        
        Args:
            sensor_type: 传感器类型，可选值: co2, gps, hum, temp, ws
        
        Returns:
            bool: 操作是否成功
        """
        if sensor_type in self.sensor_enabled:
            if self.sensor_enabled[sensor_type]:
                self.sensor_enabled[sensor_type] = False
                self.logger.info(f"已禁用 {sensor_type} 传感器")
            else:
                self.logger.info(f"{sensor_type} 传感器已经是禁用状态")
            return True
        else:
            self.logger.warning(f"未知传感器类型: {sensor_type}")
            return False
    
    def get_sensor_status(self):
        """获取所有传感器的状态
        
        Returns:
            包含所有传感器状态的字典
        """
        return {
            'cth': self.sensor_states['cth']['status'],
            'gps': self.sensor_states['gps']['status'],
            'ws': self.sensor_states['ws']['status'],
            'enabled': self.sensor_enabled
        }
        
    # 添加用于测试的方法
    def inject_test_data(self, data, skip_validation=False):
        """接收测试数据注入
        
        Args:
            data (dict): 要注入的传感器数据
            skip_validation (bool): 是否跳过数据验证，用于压力测试
            
        Returns:
            bool: 数据注入是否成功
        """
        self.logger.info(f"接收到测试数据注入: {data}")
        
        # 验证数据格式
        if not isinstance(data, dict):
            self.logger.error("注入的测试数据格式无效，应为字典类型")
            return False
            
        # 添加时间戳
        if 'timestamp' not in data:
            data['timestamp'] = int(time.time())
        if 'datetime' not in data:
            data['datetime'] = time.strftime('%Y-%m-%d %H:%M:%S')
        
        # 增加API注入数据总计数（包括正常和错误）
        self.api_data_processed_count += 1
        
        # 同步计数到MQTT客户端 - 这里先更新接口调用总数
        try:
            import builtins
            if hasattr(builtins, 'mqtt_client_instance'):
                mqtt_client = getattr(builtins, 'mqtt_client_instance')
                # 增加接口调用总数计数
                mqtt_client.sensor_data_total_count += 1
        except Exception as e:
            self.logger.error(f"同步接口调用总数计数到MQTT客户端失败: {e}")
        
        # 使用is_api_data=True标记这是API注入的数据
        self.save_sensor_data(data, is_api_data=True)
        self.logger.info("测试数据已保存")
            
        return True

    def get_data_counts(self):
        """获取API注入数据处理计数统计
        
        Returns:
            dict: 包含API注入数据的处理计数信息
        """
        return {
            "processed_count": self.api_data_processed_count,
            "normal_count": self.api_data_normal_count,
            "error_count": self.api_data_error_count,
            "last_reset_time": self.last_count_reset_time
        }
    
    def reset_data_counts(self):
        """重置API数据处理计数"""
        self.api_data_processed_count = 0
        self.api_data_normal_count = 0
        self.api_data_error_count = 0
        self.last_count_reset_time = time.time()
        self.logger.info("API数据处理计数已重置")
        
        # 同步到MQTT客户端
        try:
            import builtins
            if hasattr(builtins, 'mqtt_client_instance'):
                mqtt_client = getattr(builtins, 'mqtt_client_instance')
                mqtt_client.update_sensor_counts(0, 0, 0)
        except Exception as e:
            self.logger.error(f"同步计数重置到MQTT客户端失败: {e}")

    def _check_environment(self):
        """检查运行环境，确保路径配置正确"""
        self.logger.info(f"当前运行目录: {os.getcwd()}")
        self.logger.info(f"项目根目录: {self.root_dir}")
        self.logger.info(f"数据目录: {self.data_dir}")
        
        # 检查目录访问权限
        try:
            test_file = os.path.join(self.current_dir, '.test_write')
            try:
                with open(test_file, 'w') as f:
                    f.write('test')
                self.logger.info("数据目录写入权限测试成功")
            finally:
                if os.path.exists(test_file):
                    os.remove(test_file)
        except Exception as e:
            self.logger.error(f"数据目录写入权限测试失败: {e}")
            # 不抛出异常，让应用继续尝试运行

    def _flush_cache_thread(self):
        """内存缓存定时刷新线程"""
        self.logger.info("缓存刷新线程已启动")
        while not self.flush_event.is_set():
            try:
                # 检查是否需要刷新缓存
                current_time = time.time()
                elapsed = current_time - self.last_flush_time
                
                if (elapsed >= self.flush_interval) or (len(self.sensor_data_cache) >= self.cache_max_size) or (len(self.sensor_errors_cache) >= self.cache_max_size):
                    self._flush_cache_to_disk()
                    self.last_flush_time = current_time
                    
                # 睡眠一段时间，减少CPU占用
                time.sleep(5)
                    
            except Exception as e:
                self.logger.error(f"缓存刷新线程异常: {e}", exc_info=True)
                time.sleep(1)  # 发生错误后暂停一下
        
        self.logger.info("缓存刷新线程已停止")

    def _flush_cache_once(self):
        """单次缓存刷新（用于定期任务）"""
        try:
            current_time = time.time()
            elapsed = current_time - self.last_flush_time

            if (elapsed >= self.flush_interval) or (len(self.sensor_data_cache) >= self.cache_max_size) or (len(self.sensor_errors_cache) >= self.cache_max_size):
                self._flush_cache_to_disk()
                self.last_flush_time = current_time
                self.logger.debug("定期缓存刷新完成")
        except Exception as e:
            self.logger.error(f"定期缓存刷新失败: {e}", exc_info=True)

    def _flush_cache_to_disk(self):
        """将内存缓存写入磁盘"""
        with self.file_operation_lock:
            try:
                # 刷新传感器数据缓存
                if self.sensor_data_cache:
                    cache_count = len(self.sensor_data_cache)
                    cache_data = self.sensor_data_cache.copy()

                    # 优先使用异步I/O
                    if self.async_file_io and self.async_file_io.running:
                        # 使用异步I/O批量写入
                        from utils.async_file_io import WriteOperation
                        success = self.async_file_io.write_async(
                            self.sensor_data_file,
                            cache_data,
                            WriteOperation.MERGE,  # 使用合并模式自动去重
                            priority=1,  # 传感器数据优先级为1
                            callback=self._on_sensor_data_write_complete
                        )

                        if success:
                            self.sensor_data_cache = []
                            self.logger.info(f"已将 {cache_count} 条传感器数据提交到异步写入队列")
                        else:
                            self.logger.warning("异步写入队列已满，使用同步写入")
                            self._flush_sensor_data_sync(cache_data, cache_count)
                    else:
                        # 使用同步I/O
                        self._flush_sensor_data_sync(cache_data, cache_count)

                # 刷新错误数据缓存
                if self.sensor_errors_cache:
                    error_count = len(self.sensor_errors_cache)
                    error_data = self.sensor_errors_cache.copy()

                    # 优先使用异步I/O
                    if self.async_file_io and self.async_file_io.running:
                        from utils.async_file_io import WriteOperation
                        success = self.async_file_io.write_async(
                            self.sensor_errors_file,
                            error_data,
                            WriteOperation.APPEND,  # 错误数据使用追加模式
                            priority=2,  # 错误数据优先级为2（高于传感器数据）
                            callback=self._on_error_data_write_complete
                        )

                        if success:
                            self.sensor_errors_cache = []
                            self.logger.info(f"已将 {error_count} 条错误数据提交到异步写入队列")
                        else:
                            self.logger.warning("异步写入队列已满，使用同步写入")
                            self._flush_error_data_sync(error_data)
                    else:
                        # 使用同步I/O
                        self._flush_error_data_sync(error_data)

            except Exception as e:
                self.logger.error(f"刷新缓存到磁盘时发生异常: {e}", exc_info=True)

    def _flush_sensor_data_sync(self, cache_data, cache_count):
        """同步刷新传感器数据"""
        try:
            # 读取现有数据
            current_data = self.read_json_safe(self.sensor_data_file)

            # 添加缓存数据
            current_data.extend(cache_data)

            # 去重：只保留每个时间戳最新一条，但保留uploaded标记
            seen = {}
            deduped = []
            for item in reversed(current_data):
                t = item.get('time')
                if t not in seen:
                    seen[t] = item
                else:
                    # 如果已存在相同时间戳，保留uploaded=True的状态
                    existing_item = seen[t]
                    if item.get('uploaded', False) or existing_item.get('uploaded', False):
                        # 任一数据已上传，则标记为已上传
                        item['uploaded'] = True
                        existing_item['uploaded'] = True
                    seen[t] = item  # 使用最新的数据，但保留uploaded状态

            # 按时间戳排序重建列表
            current_data = sorted(seen.values(), key=lambda x: x.get('time', 0))

            # 写入文件
            if self._write_json_safe_internal(self.sensor_data_file, current_data):
                self.sensor_data_cache = []
                self.logger.info(f"已将 {cache_count} 条传感器数据同步写入文件")
            else:
                self.logger.error("同步写入传感器数据失败")

        except Exception as e:
            self.logger.error(f"同步刷新传感器数据失败: {e}")

    def _flush_error_data_sync(self, error_data):
        """同步刷新错误数据"""
        try:
            # 读取现有错误
            current_errors = self.read_json_safe(self.sensor_errors_file)

            # 添加缓存错误
            current_errors.extend(error_data)

            # 写入文件
            if self._write_json_safe_internal(self.sensor_errors_file, current_errors):
                self.sensor_errors_cache = []
                self.logger.info(f"已将 {len(error_data)} 条错误数据同步写入文件")
            else:
                self.logger.error("同步写入错误数据失败")

        except Exception as e:
            self.logger.error(f"同步刷新错误数据失败: {e}")

    def _on_sensor_data_write_complete(self, success, task):
        """传感器数据异步写入完成回调"""
        if success:
            self.logger.debug(f"传感器数据异步写入成功: {len(task.data)} 条")
        else:
            self.logger.error(f"传感器数据异步写入失败: {task.file_path}")

    def _on_error_data_write_complete(self, success, task):
        """错误数据异步写入完成回调"""
        if success:
            self.logger.debug(f"错误数据异步写入成功: {len(task.data)} 条")
        else:
            self.logger.error(f"错误数据异步写入失败: {task.file_path}")
    
    def _write_json_safe_internal(self, file_path, data):
        """安全写入JSON文件的内部实现（不使用锁，因为调用者已上锁）"""
        try:
            # 确保文件所在目录存在
            directory = os.path.dirname(file_path)
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                self.logger.info(f"创建目录: {directory}")
            
            # 先写入临时文件
            temp_file = f"{file_path}.tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
            
            # 然后替换原文件
            with open(file_path, 'w', encoding='utf-8') as f:
                fcntl.flock(f, fcntl.LOCK_EX)  # 获取独占锁
                try:
                    # 清空原文件内容
                    f.truncate(0)
                    # 写入新数据
                    with open(temp_file, 'r', encoding='utf-8') as temp:
                        f.write(temp.read())
                finally:
                    fcntl.flock(f, fcntl.LOCK_UN)  # 释放锁
            
            # 删除临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)
                
            return True
        except Exception as e:
            self.logger.error(f"写入JSON文件失败: {e}")
            return False

    def start_intensive_sampling(self, sensor_type, last_value, current_value):
        """启动高频采样来确认异常
        
        Args:
            sensor_type: 传感器类型（如'temperature', 'humidity'等）
            last_value: 上次测量值
            current_value: 当前测量值
        """
        self.logger.info(f"启动 {sensor_type} 高频采样: 从 {last_value} 到 {current_value}")
        
        # 创建并启动高频采样线程
        thread = threading.Thread(
            target=self._intensive_sampling_thread,
            args=(sensor_type, last_value, current_value),
            daemon=True
        )
        thread.start()

    def _intensive_sampling_thread(self, sensor_type, last_value, current_value):
        """执行高频采样，每5秒采集一次，连续20次"""
        samples = []
        sample_times = []
        
        for i in range(20):
            try:
                # 根据传感器类型获取相应数据
                if sensor_type in ['temperature', 'humidity', 'co2']:
                    data = self.get_cth_data()
                    if data:
                        if sensor_type == 'temperature' and 'temperature' in data:
                            samples.append(data['temperature'])
                        elif sensor_type == 'humidity' and 'humidity' in data:
                            samples.append(data['humidity'])
                        elif sensor_type == 'co2' and 'co2' in data:
                            samples.append(data['co2'])
                elif sensor_type == 'wind_speed':
                    data = self.get_wind_speed()
                    if data and 'wind_speed' in data:
                        samples.append(data['wind_speed'])
                
                sample_times.append(time.time())
                self.logger.debug(f"高频采样 {sensor_type} [{i+1}/20]: {samples[-1] if samples else 'N/A'}")
                
                # 等待5秒
                time.sleep(5)
                
            except Exception as e:
                self.logger.error(f"高频采样异常: {e}", exc_info=True)
        
        # 分析采样数据，确认是否为真实异常
        self._analyze_samples(sensor_type, samples, sample_times, last_value, current_value)

    def _analyze_samples(self, sensor_type, samples, sample_times, last_value, current_value):
        """分析高频采样数据，确认是否为真实异常"""
        import math  # 引入math模块用于标准差计算
        
        if not samples or len(samples) < 10:  # 确保有足够多的样本
            self.logger.warning(f"高频采样样本数不足，无法确认 {sensor_type} 异常")
            return
        
        # 计算基本统计信息
        mean_value = sum(samples) / len(samples)
        std_dev = math.sqrt(sum((x - mean_value)**2 for x in samples) / len(samples))
        max_value = max(samples)
        min_value = min(samples)
        
        # 判断数据稳定性
        is_stable = std_dev < (mean_value * 0.05) if mean_value != 0 else std_dev < 1.0  # 标准差小于平均值的5%视为稳定
        
        # 如果数据稳定且与初始异常值相近，确认为传感器异常
        if is_stable and abs(mean_value - current_value) < (mean_value * 0.1 if mean_value != 0 else 1.0):
            error_msg = (f"{sensor_type} 传感器异常确认: 从 {last_value} 突变至 {current_value}，"
                        f"高频采样结果: 平均值={mean_value:.2f}, 标准差={std_dev:.2f}, "
                        f"范围=[{min_value:.2f}, {max_value:.2f}]")
            self.logger.error(error_msg)
            
            # 通过设备自检上报异常
            self._report_confirmed_anomaly(sensor_type, error_msg)
        else:
            self.logger.info(f"{sensor_type} 高频采样结果波动较大，可能是正常环境变化")
            self.logger.debug(f"统计数据: 平均值={mean_value:.2f}, 标准差={std_dev:.2f}, 范围=[{min_value:.2f}, {max_value:.2f}]")

    def _report_confirmed_anomaly(self, sensor_type, message):
        """向设备自检系统上报确认的异常情况
        
        Args:
            sensor_type: 传感器类型
            message: 异常消息
        """
        try:
            # 获取MQTT客户端实例
            import builtins
            if hasattr(builtins, 'mqtt_client_instance'):
                mqtt_client = getattr(builtins, 'mqtt_client_instance')
                
                # 映射传感器类型到组件名
                component_map = {
                    'cth-temp': 'temp',
                    'cth-hum': 'hum',
                    'cth-co2': 'co2',
                    'gps': 'gps',
                    'ws': 'ws'
                }
                
                component = component_map.get(sensor_type)
                if not component and sensor_type == 'cth':
                    # 如果只提供了cth，默认为co2
                    component = 'co2'
                
                if not component:
                    self.logger.warning(f"未知的传感器类型: {sensor_type}")
                    return
                
                # 构建自检数据项
                check_item = {
                    "data": {
                        "co2": {"status": self.STATUS_NORMAL, "message": ""},
                        "temp": {"status": self.STATUS_NORMAL, "message": ""},
                        "hum": {"status": self.STATUS_NORMAL, "message": ""},
                        "gps": {"status": self.STATUS_NORMAL, "message": ""},
                        "ws": {"status": self.STATUS_NORMAL, "message": ""},
                        "camera": {"status": self.STATUS_NORMAL, "message": ""},
                        "fan": {"status": self.STATUS_NORMAL, "message": ""},
                        "co2device": {"status": self.STATUS_NORMAL, "message": ""},
                        "battery": {"status": self.STATUS_NORMAL, "message": ""},
                        "cpu": {"status": self.STATUS_NORMAL, "message": ""},
                        "memory": {"status": self.STATUS_NORMAL, "message": ""},
                        "disk": {"status": self.STATUS_NORMAL, "message": ""},
                        "gpu": {"status": self.STATUS_NORMAL, "message": ""}
                    },
                    "time": int(time.time()),
                    "devid": mqtt_client.device_id,
                    "ver": mqtt_client.mqtt_config.get('version', '2.0'),
                    "dir": "up"
                }
                
                # 设置对应传感器的异常状态和消息
                if component in check_item["data"]:
                    check_item["data"][component]["status"] = self.STATUS_TEMP_FAILURE
                    check_item["data"][component]["message"] = message
                
                mqtt_client.publish_device_check(check_item)
                # 标记设备自检需要立即上报
                mqtt_client.mark_device_check_for_immediate_upload()
                self.logger.info(f"已通过MQTT上报传感器异常检查: {component} - {message}")
        except Exception as e:
            self.logger.error(f"上报传感器异常失败: {e}", exc_info=True)

    def _report_sensor_recovery(self, sensor_type):
        """上报传感器恢复正常状态
        
        Args:
            sensor_type: 传感器类型
        """
        try:
            # 获取设备自检系统实例
            import builtins
            if hasattr(builtins, 'device_health_instance'):
                device_health = getattr(builtins, 'device_health_instance')
                
                # 映射传感器类型到组件名
                if sensor_type == 'cth':
                    # CO2传感器包含多个组件
                    components = ['co2', 'temp', 'hum']
                    for component in components:
                        device_health.report_component_status(component, 
                                                             device_health.STATUS_NORMAL, 
                                                             "")
                    self.logger.info(f"已上报温湿度CO2传感器恢复正常状态")
                elif sensor_type == 'gps':
                    device_health.report_component_status('gps', 
                                                         device_health.STATUS_NORMAL, 
                                                         "")
                    self.logger.info(f"已上报GPS传感器恢复正常状态")
                elif sensor_type == 'ws':
                    device_health.report_component_status('ws', 
                                                         device_health.STATUS_NORMAL, 
                                                         "")
                    self.logger.info(f"已上报风速传感器恢复正常状态")
        except Exception as e:
            self.logger.error(f"上报传感器恢复状态失败: {e}")

    def _report_sensor_failure_to_device_check(self, sensor_type, error_msg):
        """上报传感器故障到设备自检系统
        
        Args:
            sensor_type: 传感器类型
            error_msg: 错误信息
        """
        try:
            # 获取设备自检系统实例
            import builtins
            if hasattr(builtins, 'device_health_instance'):
                device_health = getattr(builtins, 'device_health_instance')
                
                # 映射传感器类型到组件名
                if sensor_type == 'cth':
                    # 根据具体情况决定哪个子传感器出现故障
                    # 简单起见，暂时全部标记为故障
                    components = ['co2', 'temp', 'hum']
                    for component in components:
                        device_health.report_component_status(component, 
                                                             device_health.STATUS_ERROR, 
                                                             f"{sensor_type}传感器永久故障: {error_msg}")
                elif sensor_type == 'gps':
                    device_health.report_component_status('gps', 
                                                         device_health.STATUS_ERROR, 
                                                         f"GPS传感器永久故障: {error_msg}")
                elif sensor_type == 'ws':
                    device_health.report_component_status('ws', 
                                                         device_health.STATUS_ERROR, 
                                                         f"风速传感器永久故障: {error_msg}")
        except Exception as e:
            self.logger.error(f"上报传感器故障失败: {e}")

# 测试代码
if __name__ == "__main__":
    collector = SensorDataCollector()
    try:
        collector.start()
        # 让采集线程运行一段时间
        time.sleep(10)
        collector.stop()
    finally:
        collector.close() 