2025-07-30 11:14:03,850 - sensor_collector - INFO - 初始化传感器收集器...
2025-07-30 11:14:03,850 - sensor_collector - INFO - 🧪 传感器收集器运行在测试模式
2025-07-30 11:14:03,850 - sensor_collector - INFO - 🧪 启用模拟硬件组件
2025-07-30 11:14:03,850 - sensor_collector - INFO - 串口配置: 端口=/dev/mock_ttyTHS1, 波特率=4800
2025-07-30 11:14:03,850 - sensor_collector - INFO - 🧪 测试模式数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 11:14:03,851 - sensor_collector - INFO - 创建目录: /home/<USER>/main_test/test_data/sensors/history
2025-07-30 11:14:03,851 - sensor_collector - INFO - 创建文件: /home/<USER>/main_test/test_data/sensors/history/sensor_data_archive.json
2025-07-30 11:14:03,851 - sensor_collector - INFO - 创建文件: /home/<USER>/main_test/test_data/sensors/history/sensor_errors_archive.json
2025-07-30 11:14:03,851 - sensor_collector - INFO - 当前运行目录: /home/<USER>/main_test
2025-07-30 11:14:03,852 - sensor_collector - INFO - 项目根目录: /home/<USER>/main_test
2025-07-30 11:14:03,852 - sensor_collector - INFO - 数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 11:14:03,852 - sensor_collector - INFO - 数据目录写入权限测试成功
2025-07-30 11:14:03,857 - sensor_collector - INFO - 🧪 模拟硬件控制器初始化成功
2025-07-30 11:14:03,857 - sensor_collector - INFO - 🧪 测试模式：跳过真实串口初始化
2025-07-30 11:14:03,861 - sensor_collector - INFO - 已注册内存监控回调
2025-07-30 11:14:03,878 - sensor_collector - INFO - 异步文件I/O初始化完成
2025-07-30 11:14:03,890 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 11:14:03,930 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 11:14:03,931 - sensor_collector - INFO - [采集线程] 开始传感器数据采集循环(采集间隔:60秒), 线程ID=281472946532640
2025-07-30 11:14:03,931 - sensor_collector - INFO - 传感器数据采集任务已提交到线程管理器
2025-07-30 11:14:03,931 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:14:03,932 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:14:03
2025-07-30 11:14:03,932 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:14:03,932 - sensor_collector - INFO - cth传感器状态变化: 断开连接 → 正常
2025-07-30 11:14:03,932 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:14:03,932 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:14:03,933 - sensor_collector - INFO - gps传感器状态变化: 断开连接 → 正常
2025-07-30 11:14:03,933 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:14:03,933 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:14:03,933 - sensor_collector - INFO - ws传感器状态变化: 断开连接 → 正常
2025-07-30 11:14:03,933 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:14:03,933 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:14:03,933 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:14:03,934 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845243, 缓存大小: 1/1000
2025-07-30 11:14:33,960 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:15:03,999 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:15:03,999 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:15:03
2025-07-30 11:15:04,000 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:15:04,000 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:15:04,000 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:15:04,000 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:15:04,000 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:15:04,001 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:15:04,001 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:15:04,001 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:15:04,002 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845303, 缓存大小: 1/1000
2025-07-30 11:15:33,978 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:16:04,069 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:16:04,070 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:16:04
2025-07-30 11:16:04,070 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:16:04,070 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:16:04,071 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:16:04,071 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:16:04,071 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:16:04,071 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:16:04,071 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:16:04,072 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:16:04,072 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845364, 缓存大小: 1/1000
2025-07-30 11:16:34,019 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:17:04,137 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:17:04,138 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:17:04
2025-07-30 11:17:04,138 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:17:04,138 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:17:04,139 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:17:04,139 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:17:04,139 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:17:04,139 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:17:04,140 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:17:04,140 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:17:04,140 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845424, 缓存大小: 1/1000
2025-07-30 11:17:34,079 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:18:04,207 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:18:04,208 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:18:04
2025-07-30 11:18:04,208 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:18:04,208 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:18:04,208 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:18:04,209 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:18:04,209 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:18:04,209 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:18:04,209 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:18:04,209 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:18:04,210 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845484, 缓存大小: 1/1000
2025-07-30 11:18:34,140 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:19:04,275 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:19:04,275 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:19:04
2025-07-30 11:19:04,276 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:19:04,276 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:19:04,276 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:19:04,276 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:19:04,277 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:19:04,277 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:19:04,277 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:19:04,278 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:19:04,278 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845544, 缓存大小: 1/1000
2025-07-30 11:19:34,181 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:20:04,344 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:20:04,345 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:20:04
2025-07-30 11:20:04,345 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:20:04,346 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:20:04,346 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:20:04,346 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:20:04,346 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:20:04,347 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:20:04,347 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:20:04,347 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:20:04,348 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845604, 缓存大小: 1/1000
2025-07-30 11:20:34,204 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:21:04,411 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:21:04,412 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:21:04
2025-07-30 11:21:04,412 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:21:04,412 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:21:04,413 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:21:04,413 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:21:04,413 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:21:04,413 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:21:04,414 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:21:04,414 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:21:04,414 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753845664, 缓存大小: 1/1000
2025-07-30 11:21:32,949 - sensor_collector - INFO - 
正在停止传感器数据采集线程...
2025-07-30 11:21:32,949 - sensor_collector - INFO - 已取消传感器数据采集任务
2025-07-30 11:21:32,950 - sensor_collector - INFO - 已取消传感器缓存刷新定期任务
2025-07-30 11:21:32,950 - sensor_collector - INFO - 执行停止前的缓存刷新...
2025-07-30 11:21:32,950 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:21:32,950 - sensor_collector - INFO - 执行停止前的数据强制备份...
2025-07-30 11:21:32,953 - sensor_collector - INFO - 已创建数据备份: /home/<USER>/main_test/test_data/sensors/current/sensor_data.json.20250730112132.bak
2025-07-30 11:21:33,435 - sensor_collector - INFO - 🧪 模拟硬件控制器已停止
2025-07-30 11:21:33,436 - sensor_collector - INFO - 传感器数据采集线程已停止
2025-07-30 11:39:56,636 - sensor_collector - INFO - 初始化传感器收集器...
2025-07-30 11:39:56,636 - sensor_collector - INFO - 🧪 传感器收集器运行在测试模式
2025-07-30 11:39:56,636 - sensor_collector - INFO - 🧪 启用模拟硬件组件
2025-07-30 11:39:56,636 - sensor_collector - INFO - 串口配置: 端口=/dev/mock_ttyTHS1, 波特率=4800
2025-07-30 11:39:56,636 - sensor_collector - INFO - 🧪 测试模式数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 11:39:56,636 - sensor_collector - INFO - 当前运行目录: /home/<USER>/main_test
2025-07-30 11:39:56,636 - sensor_collector - INFO - 项目根目录: /home/<USER>/main_test
2025-07-30 11:39:56,637 - sensor_collector - INFO - 数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 11:39:56,637 - sensor_collector - INFO - 数据目录写入权限测试成功
2025-07-30 11:39:56,642 - sensor_collector - INFO - 🧪 模拟硬件控制器初始化成功
2025-07-30 11:39:56,642 - sensor_collector - INFO - 🧪 测试模式：跳过真实串口初始化
2025-07-30 11:39:56,646 - sensor_collector - INFO - 已注册内存监控回调
2025-07-30 11:39:56,663 - sensor_collector - INFO - 异步文件I/O初始化完成
2025-07-30 11:39:56,675 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 11:39:56,717 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 11:39:56,718 - sensor_collector - INFO - [采集线程] 开始传感器数据采集循环(采集间隔:60秒), 线程ID=281472946532640
2025-07-30 11:39:56,718 - sensor_collector - INFO - 传感器数据采集任务已提交到线程管理器
2025-07-30 11:39:56,718 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:39:56,719 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:39:56
2025-07-30 11:39:56,719 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:39:56,719 - sensor_collector - INFO - cth传感器状态变化: 断开连接 → 正常
2025-07-30 11:39:56,719 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:39:56,720 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:39:56,720 - sensor_collector - INFO - gps传感器状态变化: 断开连接 → 正常
2025-07-30 11:39:56,720 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:39:56,720 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:39:56,720 - sensor_collector - INFO - ws传感器状态变化: 断开连接 → 正常
2025-07-30 11:39:56,720 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:39:56,720 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:39:56,720 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:39:56,720 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753846796, 缓存大小: 1/1000
2025-07-30 11:40:26,742 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:40:56,780 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 11:40:56,780 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 11:40:56
2025-07-30 11:40:56,781 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 11:40:56,781 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 11:40:56,781 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 11:40:56,781 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 11:40:56,781 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 11:40:56,781 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 11:40:56,781 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 11:40:56,781 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 11:40:56,782 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753846856, 缓存大小: 1/1000
2025-07-30 11:41:22,299 - sensor_collector - INFO - 
正在停止传感器数据采集线程...
2025-07-30 11:41:22,299 - sensor_collector - INFO - 已取消传感器数据采集任务
2025-07-30 11:41:22,299 - sensor_collector - INFO - 已取消传感器缓存刷新定期任务
2025-07-30 11:41:22,300 - sensor_collector - INFO - 执行停止前的缓存刷新...
2025-07-30 11:41:22,300 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 11:41:22,300 - sensor_collector - INFO - 执行停止前的数据强制备份...
2025-07-30 11:41:22,302 - sensor_collector - INFO - 已创建数据备份: /home/<USER>/main_test/test_data/sensors/current/sensor_data.json.20250730114122.bak
2025-07-30 11:41:22,736 - sensor_collector - INFO - 🧪 模拟硬件控制器已停止
2025-07-30 11:41:22,736 - sensor_collector - INFO - 传感器数据采集线程已停止
2025-07-30 12:02:55,305 - sensor_collector - INFO - 初始化传感器收集器...
2025-07-30 12:02:55,305 - sensor_collector - INFO - 🧪 传感器收集器运行在测试模式
2025-07-30 12:02:55,305 - sensor_collector - INFO - 🧪 启用模拟硬件组件
2025-07-30 12:02:55,306 - sensor_collector - INFO - 串口配置: 端口=/dev/mock_ttyTHS1, 波特率=4800
2025-07-30 12:02:55,306 - sensor_collector - INFO - 🧪 测试模式数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 12:02:55,306 - sensor_collector - INFO - 当前运行目录: /home/<USER>/main_test
2025-07-30 12:02:55,306 - sensor_collector - INFO - 项目根目录: /home/<USER>/main_test
2025-07-30 12:02:55,306 - sensor_collector - INFO - 数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 12:02:55,306 - sensor_collector - INFO - 数据目录写入权限测试成功
2025-07-30 12:02:55,311 - sensor_collector - INFO - 🧪 模拟硬件控制器初始化成功
2025-07-30 12:02:55,312 - sensor_collector - INFO - 🧪 测试模式：跳过真实串口初始化
2025-07-30 12:02:55,315 - sensor_collector - INFO - 已注册内存监控回调
2025-07-30 12:02:55,334 - sensor_collector - INFO - 异步文件I/O初始化完成
2025-07-30 12:02:55,346 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 12:02:55,357 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 12:02:55,358 - sensor_collector - INFO - [采集线程] 开始传感器数据采集循环(采集间隔:60秒), 线程ID=281473273622816
2025-07-30 12:02:55,358 - sensor_collector - INFO - 传感器数据采集任务已提交到线程管理器
2025-07-30 12:02:55,358 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:02:55,359 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:02:55
2025-07-30 12:02:55,359 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:02:55,359 - sensor_collector - INFO - cth传感器状态变化: 断开连接 → 正常
2025-07-30 12:02:55,359 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:02:55,359 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:02:55,360 - sensor_collector - INFO - gps传感器状态变化: 断开连接 → 正常
2025-07-30 12:02:55,360 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:02:55,360 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:02:55,360 - sensor_collector - INFO - ws传感器状态变化: 断开连接 → 正常
2025-07-30 12:02:55,360 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:02:55,360 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:02:55,360 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:02:55,360 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848175, 缓存大小: 1/1000
2025-07-30 12:03:25,387 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:03:39,814 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848219, 'datetime': '2025-07-30 12:03:39', 'co2': 450}
2025-07-30 12:03:39,815 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848219, 缓存大小: 1/1000
2025-07-30 12:03:39,815 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:03:46,272 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848226, 'datetime': '2025-07-30 12:03:46', 'temperature': 25.5}
2025-07-30 12:03:46,272 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848226, 缓存大小: 2/1000
2025-07-30 12:03:46,272 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:03:53,578 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848233, 'datetime': '2025-07-30 12:03:53', 'humidity': 65.2}
2025-07-30 12:03:53,578 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848233, 缓存大小: 3/1000
2025-07-30 12:03:53,578 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:03:55,420 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:03:55,420 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:03:55
2025-07-30 12:03:55,421 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:03:55,421 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:03:55,421 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:03:55,421 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:03:55,421 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:03:55,422 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:03:55,422 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:03:55,422 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:03:55,422 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848235, 缓存大小: 4/1000
2025-07-30 12:04:25,442 - sensor_collector - INFO - 已将 4 条传感器数据提交到异步写入队列
2025-07-30 12:04:55,482 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:04:55,482 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:04:55
2025-07-30 12:04:55,482 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:04:55,483 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:04:55,483 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:04:55,483 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:04:55,483 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:04:55,483 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:04:55,483 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:04:55,483 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:04:55,484 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848295, 缓存大小: 1/1000
2025-07-30 12:04:55,727 - sensor_collector - INFO - 
正在停止传感器数据采集线程...
2025-07-30 12:04:55,727 - sensor_collector - INFO - 已取消传感器数据采集任务
2025-07-30 12:04:55,728 - sensor_collector - INFO - 已取消传感器缓存刷新定期任务
2025-07-30 12:04:55,728 - sensor_collector - INFO - 执行停止前的缓存刷新...
2025-07-30 12:04:55,728 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:04:55,729 - sensor_collector - INFO - 执行停止前的数据强制备份...
2025-07-30 12:04:55,732 - sensor_collector - INFO - 已创建数据备份: /home/<USER>/main_test/test_data/sensors/current/sensor_data.json.20250730120455.bak
2025-07-30 12:04:56,438 - sensor_collector - INFO - 🧪 模拟硬件控制器已停止
2025-07-30 12:04:56,438 - sensor_collector - INFO - 传感器数据采集线程已停止
2025-07-30 12:10:03,825 - sensor_collector - INFO - 初始化传感器收集器...
2025-07-30 12:10:03,825 - sensor_collector - INFO - 🧪 传感器收集器运行在测试模式
2025-07-30 12:10:03,825 - sensor_collector - INFO - 🧪 启用模拟硬件组件
2025-07-30 12:10:03,825 - sensor_collector - INFO - 串口配置: 端口=/dev/mock_ttyTHS1, 波特率=4800
2025-07-30 12:10:03,825 - sensor_collector - INFO - 🧪 测试模式数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 12:10:03,826 - sensor_collector - INFO - 当前运行目录: /home/<USER>/main_test
2025-07-30 12:10:03,826 - sensor_collector - INFO - 项目根目录: /home/<USER>/main_test
2025-07-30 12:10:03,826 - sensor_collector - INFO - 数据目录: /home/<USER>/main_test/test_data/sensors
2025-07-30 12:10:03,826 - sensor_collector - INFO - 数据目录写入权限测试成功
2025-07-30 12:10:03,827 - sensor_collector - INFO - 🧪 模拟硬件控制器初始化成功
2025-07-30 12:10:03,828 - sensor_collector - INFO - 🧪 测试模式：跳过真实串口初始化
2025-07-30 12:10:03,829 - sensor_collector - INFO - 已注册内存监控回调
2025-07-30 12:10:03,842 - sensor_collector - INFO - 异步文件I/O初始化完成
2025-07-30 12:10:03,849 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 12:10:03,855 - sensor_collector - INFO - 线程管理器初始化完成
2025-07-30 12:10:03,856 - sensor_collector - INFO - [采集线程] 开始传感器数据采集循环(采集间隔:60秒), 线程ID=281473223422240
2025-07-30 12:10:03,856 - sensor_collector - INFO - 传感器数据采集任务已提交到线程管理器
2025-07-30 12:10:03,856 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:10:03,856 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:10:03
2025-07-30 12:10:03,857 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:10:03,857 - sensor_collector - INFO - cth传感器状态变化: 断开连接 → 正常
2025-07-30 12:10:03,857 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:10:03,857 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:10:03,857 - sensor_collector - INFO - gps传感器状态变化: 断开连接 → 正常
2025-07-30 12:10:03,857 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:10:03,857 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:10:03,857 - sensor_collector - INFO - ws传感器状态变化: 断开连接 → 正常
2025-07-30 12:10:03,857 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:10:03,858 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:10:03,858 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:10:03,858 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848603, 缓存大小: 1/1000
2025-07-30 12:10:33,883 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:11:03,917 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:11:03,918 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:11:03
2025-07-30 12:11:03,918 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:11:03,918 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:11:03,918 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:11:03,918 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:11:03,918 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:11:03,919 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:11:03,919 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:11:03,919 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:11:03,919 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848663, 缓存大小: 1/1000
2025-07-30 12:11:33,934 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:12:03,979 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:12:03,979 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:12:03
2025-07-30 12:12:03,980 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:12:03,980 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:12:03,980 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:12:03,980 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:12:03,980 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:12:03,980 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:12:03,980 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:12:03,981 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:12:03,981 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848723, 缓存大小: 1/1000
2025-07-30 12:12:33,991 - sensor_collector - INFO - 已将 1 条传感器数据提交到异步写入队列
2025-07-30 12:12:51,309 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848771, 'datetime': '2025-07-30 12:12:51', 'co2': 401, 'temperature': 26, 'humidity': 61}
2025-07-30 12:12:51,310 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848771, 缓存大小: 1/1000
2025-07-30 12:12:51,310 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:52,330 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848772, 'datetime': '2025-07-30 12:12:52', 'co2': 402, 'temperature': 27, 'humidity': 62}
2025-07-30 12:12:52,330 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848772, 缓存大小: 2/1000
2025-07-30 12:12:52,330 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:53,349 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848773, 'datetime': '2025-07-30 12:12:53', 'co2': 403, 'temperature': 28, 'humidity': 63}
2025-07-30 12:12:53,349 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848773, 缓存大小: 3/1000
2025-07-30 12:12:53,350 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:54,369 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848774, 'datetime': '2025-07-30 12:12:54', 'co2': 404, 'temperature': 29, 'humidity': 64}
2025-07-30 12:12:54,370 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848774, 缓存大小: 4/1000
2025-07-30 12:12:54,370 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:55,389 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848775, 'datetime': '2025-07-30 12:12:55', 'co2': 405, 'temperature': 25, 'humidity': 65}
2025-07-30 12:12:55,390 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848775, 缓存大小: 5/1000
2025-07-30 12:12:55,390 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:56,409 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848776, 'datetime': '2025-07-30 12:12:56', 'co2': 406, 'temperature': 26, 'humidity': 66}
2025-07-30 12:12:56,409 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848776, 缓存大小: 6/1000
2025-07-30 12:12:56,409 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:57,429 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848777, 'datetime': '2025-07-30 12:12:57', 'co2': 407, 'temperature': 27, 'humidity': 67}
2025-07-30 12:12:57,429 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848777, 缓存大小: 7/1000
2025-07-30 12:12:57,430 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:58,449 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848778, 'datetime': '2025-07-30 12:12:58', 'co2': 408, 'temperature': 28, 'humidity': 68}
2025-07-30 12:12:58,449 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848778, 缓存大小: 8/1000
2025-07-30 12:12:58,450 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:12:59,469 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848779, 'datetime': '2025-07-30 12:12:59', 'co2': 409, 'temperature': 29, 'humidity': 69}
2025-07-30 12:12:59,469 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848779, 缓存大小: 9/1000
2025-07-30 12:12:59,469 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:00,489 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848780, 'datetime': '2025-07-30 12:13:00', 'co2': 410, 'temperature': 25, 'humidity': 60}
2025-07-30 12:13:00,489 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848780, 缓存大小: 10/1000
2025-07-30 12:13:00,489 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:04,041 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:13:04,041 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:13:04
2025-07-30 12:13:04,041 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:13:04,042 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:13:04,042 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:13:04,042 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:13:04,042 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:13:04,042 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:13:04,042 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:13:04,043 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:13:04,043 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848784, 缓存大小: 11/1000
2025-07-30 12:13:19,347 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 410, 'temperature': 25.1, 'humidity': 61.0}
2025-07-30 12:13:19,348 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848799, 缓存大小: 12/1000
2025-07-30 12:13:19,348 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,348 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 415, 'temperature': 25.3, 'humidity': 62.0}
2025-07-30 12:13:19,348 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,349 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 420, 'temperature': 25.5, 'humidity': 63.0}
2025-07-30 12:13:19,349 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,349 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 425, 'temperature': 25.7, 'humidity': 64.0}
2025-07-30 12:13:19,349 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,349 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 430, 'temperature': 25.9, 'humidity': 65.0}
2025-07-30 12:13:19,349 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,350 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 435, 'temperature': 26.1, 'humidity': 66.0}
2025-07-30 12:13:19,350 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,350 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 440, 'temperature': 26.3, 'humidity': 67.0}
2025-07-30 12:13:19,350 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,350 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 445, 'temperature': 26.5, 'humidity': 68.0}
2025-07-30 12:13:19,350 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,350 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 450, 'temperature': 26.7, 'humidity': 69.0}
2025-07-30 12:13:19,351 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,351 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 455, 'temperature': 26.9, 'humidity': 70.0}
2025-07-30 12:13:19,351 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,351 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 460, 'temperature': 27.1, 'humidity': 71.0}
2025-07-30 12:13:19,351 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,351 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 465, 'temperature': 27.3, 'humidity': 72.0}
2025-07-30 12:13:19,351 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,351 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 470, 'temperature': 27.5, 'humidity': 73.0}
2025-07-30 12:13:19,351 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,352 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 475, 'temperature': 27.7, 'humidity': 74.0}
2025-07-30 12:13:19,352 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,352 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 480, 'temperature': 27.9, 'humidity': 75.0}
2025-07-30 12:13:19,352 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,352 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 485, 'temperature': 28.1, 'humidity': 76.0}
2025-07-30 12:13:19,352 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,352 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 490, 'temperature': 28.3, 'humidity': 77.0}
2025-07-30 12:13:19,352 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,352 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 495, 'temperature': 28.5, 'humidity': 78.0}
2025-07-30 12:13:19,353 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,353 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 500, 'temperature': 28.7, 'humidity': 79.0}
2025-07-30 12:13:19,353 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:19,353 - sensor_collector - INFO - 接收到测试数据注入: {'co2': 505, 'temperature': 28.9, 'humidity': 80.0}
2025-07-30 12:13:19,353 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:34,046 - sensor_collector - INFO - 已将 12 条传感器数据提交到异步写入队列
2025-07-30 12:13:40,894 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 568, 'temperature': 24, 'humidity': 60}
2025-07-30 12:13:40,895 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848820, 缓存大小: 1/1000
2025-07-30 12:13:40,895 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,896 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 594, 'temperature': 33, 'humidity': 79}
2025-07-30 12:13:40,900 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 423, 'temperature': 20, 'humidity': 68}
2025-07-30 12:13:40,901 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,901 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,906 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 522, 'temperature': 34, 'humidity': 52}
2025-07-30 12:13:40,907 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 520, 'temperature': 30, 'humidity': 53}
2025-07-30 12:13:40,907 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,908 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,909 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 406, 'temperature': 20, 'humidity': 61}
2025-07-30 12:13:40,911 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 436, 'temperature': 22, 'humidity': 75}
2025-07-30 12:13:40,912 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,913 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 553, 'temperature': 31, 'humidity': 77}
2025-07-30 12:13:40,914 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,915 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,917 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 546, 'temperature': 24, 'humidity': 77}
2025-07-30 12:13:40,918 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848820, 'datetime': '2025-07-30 12:13:40', 'co2': 421, 'temperature': 25, 'humidity': 51}
2025-07-30 12:13:40,920 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:40,920 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,910 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 587, 'temperature': 29, 'humidity': 55}
2025-07-30 12:13:51,911 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 445, 'temperature': 27, 'humidity': 61}
2025-07-30 12:13:51,912 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 512, 'temperature': 32, 'humidity': 67}
2025-07-30 12:13:51,913 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848831, 缓存大小: 2/1000
2025-07-30 12:13:51,914 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 540, 'temperature': 23, 'humidity': 76}
2025-07-30 12:13:51,916 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 570, 'temperature': 23, 'humidity': 51}
2025-07-30 12:13:51,917 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,917 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,917 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,917 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,918 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 560, 'temperature': 20, 'humidity': 78}
2025-07-30 12:13:51,919 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,922 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 514, 'temperature': 31, 'humidity': 56}
2025-07-30 12:13:51,923 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 513, 'temperature': 30, 'humidity': 51}
2025-07-30 12:13:51,925 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 531, 'temperature': 23, 'humidity': 66}
2025-07-30 12:13:51,927 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848831, 'datetime': '2025-07-30 12:13:51', 'co2': 442, 'temperature': 28, 'humidity': 79}
2025-07-30 12:13:51,929 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,933 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,933 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,934 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:13:51,935 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:04,103 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:14:04,103 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:14:04
2025-07-30 12:14:04,103 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:14:04,104 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:14:04,104 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:14:04,104 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:14:04,104 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:14:04,104 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:14:04,104 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:14:04,105 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:14:04,105 - sensor_collector - ERROR - 保存传感器数据失败: '<' not supported between instances of 'dict' and 'int'
Traceback (most recent call last):
  File "/home/<USER>/main_test/sensors/sensor_collector.py", line 976, in save_sensor_data
    data_valid, data_modified = self.verify_data_quality(data, is_api_data)
  File "/home/<USER>/main_test/sensors/sensor_collector.py", line 901, in verify_data_quality
    if temp < temp_min or temp > temp_max:
TypeError: '<' not supported between instances of 'dict' and 'int'
2025-07-30 12:14:34,102 - sensor_collector - INFO - 已将 2 条传感器数据提交到异步写入队列
2025-07-30 12:14:36,558 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848876, 'datetime': '2025-07-30 12:14:36', 'co2': 619, 'temperature': 22, 'humidity': 78}
2025-07-30 12:14:36,559 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848876, 缓存大小: 1/1000
2025-07-30 12:14:36,559 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:37,079 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848877, 'datetime': '2025-07-30 12:14:37', 'co2': 521, 'temperature': 36, 'humidity': 43}
2025-07-30 12:14:37,079 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848877, 缓存大小: 2/1000
2025-07-30 12:14:37,080 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:37,598 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848877, 'datetime': '2025-07-30 12:14:37', 'co2': 638, 'temperature': 28, 'humidity': 71}
2025-07-30 12:14:37,598 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:38,116 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848878, 'datetime': '2025-07-30 12:14:38', 'co2': 527, 'temperature': 35, 'humidity': 51}
2025-07-30 12:14:38,116 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848878, 缓存大小: 3/1000
2025-07-30 12:14:38,116 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:38,634 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848878, 'datetime': '2025-07-30 12:14:38', 'co2': 464, 'temperature': 34, 'humidity': 69}
2025-07-30 12:14:38,635 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:39,153 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848879, 'datetime': '2025-07-30 12:14:39', 'co2': 638, 'temperature': 37, 'humidity': 65}
2025-07-30 12:14:39,153 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848879, 缓存大小: 4/1000
2025-07-30 12:14:39,153 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:39,672 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848879, 'datetime': '2025-07-30 12:14:39', 'co2': 475, 'temperature': 31, 'humidity': 72}
2025-07-30 12:14:39,672 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:40,190 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848880, 'datetime': '2025-07-30 12:14:40', 'co2': 649, 'temperature': 39, 'humidity': 56}
2025-07-30 12:14:40,190 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848880, 缓存大小: 5/1000
2025-07-30 12:14:40,190 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:40,708 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848880, 'datetime': '2025-07-30 12:14:40', 'co2': 582, 'temperature': 31, 'humidity': 74}
2025-07-30 12:14:40,709 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:41,227 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848881, 'datetime': '2025-07-30 12:14:41', 'co2': 403, 'temperature': 24, 'humidity': 40}
2025-07-30 12:14:41,228 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848881, 缓存大小: 6/1000
2025-07-30 12:14:41,228 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:41,746 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848881, 'datetime': '2025-07-30 12:14:41', 'co2': 589, 'temperature': 34, 'humidity': 47}
2025-07-30 12:14:41,747 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:42,265 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848882, 'datetime': '2025-07-30 12:14:42', 'co2': 683, 'temperature': 29, 'humidity': 49}
2025-07-30 12:14:42,266 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848882, 缓存大小: 7/1000
2025-07-30 12:14:42,266 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:42,784 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848882, 'datetime': '2025-07-30 12:14:42', 'co2': 482, 'temperature': 24, 'humidity': 40}
2025-07-30 12:14:42,786 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:43,307 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848883, 'datetime': '2025-07-30 12:14:43', 'co2': 658, 'temperature': 34, 'humidity': 65}
2025-07-30 12:14:43,308 - sensor_collector - INFO - 传感器数据已保存到缓存: 时间戳=1753848883, 缓存大小: 8/1000
2025-07-30 12:14:43,308 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:14:43,826 - sensor_collector - INFO - 接收到测试数据注入: {'timestamp': 1753848883, 'datetime': '2025-07-30 12:14:43', 'co2': 544, 'temperature': 31, 'humidity': 70}
2025-07-30 12:14:43,827 - sensor_collector - INFO - 测试数据已保存
2025-07-30 12:15:04,167 - sensor_collector - INFO - --- 开始传感器数据采集周期 ---
2025-07-30 12:15:04,167 - sensor_collector - INFO - 开始采集传感器数据: 2025-07-30 12:15:04
2025-07-30 12:15:04,168 - sensor_collector - INFO - 开始采集温湿度CO2数据...
2025-07-30 12:15:04,168 - sensor_collector - INFO - 温湿度CO2数据采集成功
2025-07-30 12:15:04,168 - sensor_collector - INFO - 开始采集GPS数据...
2025-07-30 12:15:04,168 - sensor_collector - INFO - GPS数据采集成功
2025-07-30 12:15:04,168 - sensor_collector - INFO - 开始采集风速数据...
2025-07-30 12:15:04,168 - sensor_collector - INFO - 风速数据采集成功
2025-07-30 12:15:04,168 - sensor_collector - INFO - 成功采集到 6 项传感器数据
2025-07-30 12:15:04,169 - sensor_collector - INFO - 完成数据质量验证
2025-07-30 12:15:04,169 - sensor_collector - ERROR - 保存传感器数据失败: '<' not supported between instances of 'dict' and 'int'
Traceback (most recent call last):
  File "/home/<USER>/main_test/sensors/sensor_collector.py", line 976, in save_sensor_data
    data_valid, data_modified = self.verify_data_quality(data, is_api_data)
  File "/home/<USER>/main_test/sensors/sensor_collector.py", line 901, in verify_data_quality
    if temp < temp_min or temp > temp_max:
TypeError: '<' not supported between instances of 'dict' and 'int'
2025-07-30 12:15:34,155 - sensor_collector - INFO - 已将 8 条传感器数据提交到异步写入队列
2025-07-30 12:15:44,397 - sensor_collector - INFO - 
正在停止传感器数据采集线程...
2025-07-30 12:15:44,397 - sensor_collector - INFO - 已取消传感器数据采集任务
2025-07-30 12:15:44,397 - sensor_collector - INFO - 已取消传感器缓存刷新定期任务
2025-07-30 12:15:44,397 - sensor_collector - INFO - 执行停止前的缓存刷新...
2025-07-30 12:15:44,397 - sensor_collector - INFO - 执行停止前的数据强制备份...
2025-07-30 12:15:44,400 - sensor_collector - INFO - 已创建数据备份: /home/<USER>/main_test/test_data/sensors/current/sensor_data.json.20250730121544.bak
2025-07-30 12:15:45,194 - sensor_collector - INFO - 🧪 模拟硬件控制器已停止
2025-07-30 12:15:45,194 - sensor_collector - INFO - 传感器数据采集线程已停止
