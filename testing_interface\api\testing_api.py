#!/usr/bin/env python3
"""
IoT传感器监控系统 - HTTP测试接口
提供完整的测试API，支持数据注入、系统控制、性能监控等功能
"""

import json
import time
import threading
import psutil
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from flask import Flask, request, jsonify, render_template_string
from werkzeug.serving import make_server

class TestingAPI:
    """HTTP测试接口服务器"""
    
    def __init__(self, sensor_system):
        self.sensor_system = sensor_system
        self.app = Flask(__name__)
        self.server = None
        self.server_thread = None
        
        # 测试状态
        self.test_sessions = {}
        self.current_session_id = None
        
        # 性能监控
        self.performance_data = []
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # 设置路由
        self._setup_routes()
        
        # 配置Flask
        self.app.config['JSON_AS_ASCII'] = False
        self.app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True
    
    def _setup_routes(self):
        """设置API路由"""
        
        # 基础信息接口
        @self.app.route('/', methods=['GET'])
        def index():
            return self._render_api_docs()
        
        @self.app.route('/api/info', methods=['GET'])
        def get_system_info():
            """获取系统基本信息"""
            return self._get_system_info()
        
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'test_mode': self.sensor_system.test_mode,
                'uptime': time.time() - getattr(self.sensor_system, 'start_time', time.time())
            })
        
        # 系统控制接口
        @self.app.route('/api/system/status', methods=['GET'])
        def get_system_status():
            """获取系统状态"""
            return self._get_system_status()
        
        @self.app.route('/api/system/start', methods=['POST'])
        def start_system():
            """启动系统"""
            return self._control_system('start')
        
        @self.app.route('/api/system/stop', methods=['POST'])
        def stop_system():
            """停止系统"""
            return self._control_system('stop')
        
        @self.app.route('/api/system/restart', methods=['POST'])
        def restart_system():
            """重启系统"""
            return self._control_system('restart')

        @self.app.route('/api/system/power', methods=['POST'])
        def control_power():
            """电源控制"""
            return self._control_power()

        @self.app.route('/api/system/components', methods=['GET'])
        def get_components_status():
            """获取组件状态"""
            return self._get_components_status()

        @self.app.route('/api/system/components/<component_name>', methods=['POST'])
        def control_component(component_name):
            """控制单个组件"""
            return self._control_component(component_name)

        @self.app.route('/api/system/sensors', methods=['GET'])
        def get_sensors_status():
            """获取传感器状态"""
            return self._get_sensors_status()

        @self.app.route('/api/system/sensors/<sensor_name>', methods=['POST'])
        def control_sensor(sensor_name):
            """控制单个传感器"""
            return self._control_sensor(sensor_name)
        
        # 数据注入接口
        @self.app.route('/api/data/inject/sensor', methods=['POST'])
        def inject_sensor_data():
            """注入传感器数据"""
            return self._inject_sensor_data()

        @self.app.route('/api/data/inject/batch', methods=['POST'])
        def inject_batch_data():
            """批量注入传感器数据"""
            return self._inject_batch_data()
        
        @self.app.route('/api/data/inject/error', methods=['POST'])
        def inject_error_data():
            """注入错误数据"""
            return self._inject_error_data()
        
        @self.app.route('/api/data/inject/batch', methods=['POST'])
        def inject_batch_data():
            """批量注入数据"""
            return self._inject_batch_data()

        @self.app.route('/api/data/inject/mosquito', methods=['POST'])
        def inject_mosquito_data():
            """注入蚊虫检测数据"""
            return self._inject_mosquito_data()

        # 数据查询接口
        @self.app.route('/api/data/sensors/latest', methods=['GET'])
        def get_latest_sensor_data():
            """获取最新传感器数据"""
            return self._get_latest_sensor_data()

        @self.app.route('/api/data/sensors/history', methods=['GET'])
        def get_sensor_history():
            """获取传感器历史数据"""
            return self._get_sensor_history()

        @self.app.route('/api/data/stats', methods=['GET'])
        def get_data_stats():
            """获取数据统计信息"""
            return self._get_data_stats()

        # 性能监控接口
        @self.app.route('/api/monitor/start', methods=['POST'])
        def start_monitoring():
            """开始性能监控"""
            return self._start_performance_monitoring()
        
        @self.app.route('/api/monitor/stop', methods=['POST'])
        def stop_monitoring():
            """停止性能监控"""
            return self._stop_performance_monitoring()
        
        @self.app.route('/api/monitor/data', methods=['GET'])
        def get_monitoring_data():
            """获取监控数据"""
            return self._get_monitoring_data()
        
        # 模拟数据管理接口
        @self.app.route('/api/mock/mqtt/messages', methods=['GET'])
        def get_mqtt_messages():
            """获取模拟MQTT消息"""
            return self._get_mock_mqtt_messages()
        
        @self.app.route('/api/mock/mqtt/clear', methods=['POST'])
        def clear_mqtt_messages():
            """清空MQTT消息历史"""
            return self._clear_mock_mqtt_messages()
        
        @self.app.route('/api/mock/network/failure', methods=['POST'])
        def simulate_network_failure():
            """模拟网络故障"""
            return self._simulate_network_failure()
        
        # 测试环境控制接口
        @self.app.route('/api/test/env/status', methods=['GET'])
        def get_test_env_status():
            """获取测试环境状态"""
            return self._get_test_env_status()

        @self.app.route('/api/test/env/reset', methods=['POST'])
        def reset_test_env():
            """重置测试环境"""
            return self._reset_test_env()

        @self.app.route('/api/test/env/config', methods=['GET'])
        def get_test_config():
            """获取测试配置"""
            return self._get_test_config()

        @self.app.route('/api/test/env/config', methods=['POST'])
        def update_test_config():
            """更新测试配置"""
            return self._update_test_config()

        # 测试管理接口
        @self.app.route('/api/test/session/start', methods=['POST'])
        def start_test_session():
            """开始测试会话"""
            return self._start_test_session()

        @self.app.route('/api/test/session/stop', methods=['POST'])
        def stop_test_session():
            """结束测试会话"""
            return self._stop_test_session()

        @self.app.route('/api/test/session/status', methods=['GET'])
        def get_test_session_status():
            """获取测试会话状态"""
            return self._get_test_session_status()
        
        # 错误处理
        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({
                'error': 'API接口不存在',
                'message': '请检查请求路径是否正确',
                'available_endpoints': self._get_available_endpoints()
            }), 404
        
        @self.app.errorhandler(500)
        def internal_error(error):
            return jsonify({
                'error': '服务器内部错误',
                'message': str(error),
                'timestamp': datetime.now().isoformat()
            }), 500
    
    def _render_api_docs(self):
        """渲染API文档页面"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>IoT传感器监控系统 - 测试API</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
                .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .method { display: inline-block; padding: 3px 8px; border-radius: 3px; color: white; font-weight: bold; }
                .get { background: #28a745; }
                .post { background: #007bff; }
                .put { background: #ffc107; color: black; }
                .delete { background: #dc3545; }
                code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🧪 IoT传感器监控系统 - 测试API</h1>
                <p>提供完整的HTTP测试接口，支持数据注入、系统控制、性能监控等功能</p>
                <p><strong>测试模式:</strong> {{ test_mode }} | <strong>当前时间:</strong> {{ current_time }}</p>
            </div>
            
            <h2>📋 可用接口</h2>
            
            <div class="endpoint">
                <span class="method get">GET</span> <code>/api/info</code>
                <p>获取系统基本信息</p>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span> <code>/api/health</code>
                <p>健康检查</p>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span> <code>/api/system/status</code>
                <p>获取系统运行状态</p>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span> <code>/api/data/inject/sensor</code>
                <p>注入传感器数据</p>
            </div>
            
            <div class="endpoint">
                <span class="method post">POST</span> <code>/api/monitor/start</code>
                <p>开始性能监控</p>
            </div>
            
            <div class="endpoint">
                <span class="method get">GET</span> <code>/api/mock/mqtt/messages</code>
                <p>获取模拟MQTT消息</p>
            </div>
            
            <p><em>更多接口请参考API文档或使用工具探索</em></p>
        </body>
        </html>
        """
        
        return render_template_string(html_template, 
                                    test_mode=self.sensor_system.test_mode,
                                    current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    def _get_system_info(self):
        """获取系统基本信息"""
        try:
            return jsonify({
                'system': {
                    'name': 'IoT传感器监控系统',
                    'version': '2.0.0',
                    'test_mode': self.sensor_system.test_mode,
                    'config_file': getattr(self.sensor_system, 'config_file', 'config/config.yaml'),
                    'start_time': getattr(self.sensor_system, 'start_time', time.time()),
                    'uptime_seconds': time.time() - getattr(self.sensor_system, 'start_time', time.time())
                },
                'components': {
                    'sensor_collector': self.sensor_system.sensor_collector is not None,
                    'mqtt_client': self.sensor_system.mqtt_client is not None,
                    'co2_controller': self.sensor_system.co2_controller is not None,
                    'device_health': self.sensor_system.device_health is not None
                },
                'mock_components': {
                    'mock_mqtt_client': hasattr(self.sensor_system, 'mock_mqtt_client') and self.sensor_system.mock_mqtt_client is not None,
                    'mock_hardware': getattr(self.sensor_system.sensor_collector, 'mock_controller', None) is not None if self.sensor_system.sensor_collector else False
                },
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            return jsonify({'error': f'获取系统信息失败: {str(e)}'}), 500

    def _get_system_status(self):
        """获取系统状态"""
        try:
            status = {
                'running': self.sensor_system.running,
                'power_status': getattr(self.sensor_system, 'power_status', 1),
                'components': {},
                'sensors': {},
                'performance': self._get_current_performance(),
                'timestamp': datetime.now().isoformat()
            }

            # 组件状态
            if self.sensor_system.sensor_collector:
                status['components']['sensor_collector'] = {
                    'running': getattr(self.sensor_system.sensor_collector, 'running', False),
                    'test_mode': getattr(self.sensor_system.sensor_collector, 'test_mode', False),
                    'mock_hardware': getattr(self.sensor_system.sensor_collector, 'mock_hardware', False)
                }

            if self.sensor_system.mqtt_client:
                status['components']['mqtt_client'] = {
                    'connected': getattr(self.sensor_system.mqtt_client, 'connected', False),
                    'is_mock': hasattr(self.sensor_system, 'mock_mqtt_client')
                }

            # 传感器状态
            if self.sensor_system.sensor_collector:
                sensor_states = getattr(self.sensor_system.sensor_collector, 'sensor_states', {})
                status['sensors'] = {
                    sensor: {
                        'status': state.get('status', 'unknown'),
                        'last_success': state.get('last_success'),
                        'error_message': state.get('error_message')
                    }
                    for sensor, state in sensor_states.items()
                }

            return jsonify(status)

        except Exception as e:
            return jsonify({'error': f'获取系统状态失败: {str(e)}'}), 500

    def _control_system(self, action):
        """控制系统操作"""
        try:
            if action == 'start':
                if not self.sensor_system.running:
                    self.sensor_system.start()
                    return jsonify({'message': '系统启动成功', 'action': action})
                else:
                    return jsonify({'message': '系统已在运行', 'action': action})

            elif action == 'stop':
                if self.sensor_system.running:
                    self.sensor_system.stop()
                    return jsonify({'message': '系统停止成功', 'action': action})
                else:
                    return jsonify({'message': '系统已停止', 'action': action})

            elif action == 'restart':
                self.sensor_system.restart()
                return jsonify({'message': '系统重启成功', 'action': action})

            else:
                return jsonify({'error': f'不支持的操作: {action}'}), 400

        except Exception as e:
            return jsonify({'error': f'系统控制失败: {str(e)}'}), 500

    def _inject_sensor_data(self):
        """注入传感器数据"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': '请提供JSON数据'}), 400

            # 支持两种数据格式：
            # 格式1: {"timestamp": xxx, "data": {...}}  (完整格式)
            # 格式2: {"sensor_type": "co2", "value": 450, "unit": "ppm", "timestamp": xxx}  (简化格式)

            if 'data' in data and 'timestamp' in data:
                # 完整格式，直接使用
                inject_data = data
            else:
                # 简化格式，需要转换
                if 'timestamp' not in data:
                    data['timestamp'] = int(time.time())

                # 构造完整格式
                inject_data = {
                    'timestamp': data['timestamp'],
                    'datetime': datetime.fromtimestamp(data['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                }

                # 添加传感器数据
                if 'sensor_type' in data and 'value' in data:
                    inject_data[data['sensor_type']] = data['value']
                else:
                    # 如果没有sensor_type，直接复制所有字段（除了timestamp）
                    for key, value in data.items():
                        if key not in ['timestamp']:
                            inject_data[key] = value

            # 注入数据到传感器收集器
            if self.sensor_system.sensor_collector:
                result = self.sensor_system.sensor_collector.inject_test_data(inject_data)
                if result:
                    return jsonify({
                        'message': '数据注入成功',
                        'injected_data': inject_data,
                        'original_data': data,
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    return jsonify({'error': '数据注入失败'}), 500
            else:
                return jsonify({'error': '传感器收集器未初始化'}), 500

        except Exception as e:
            return jsonify({'error': f'数据注入失败: {str(e)}'}), 500

    def _inject_batch_data(self):
        """批量注入传感器数据"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': '请提供JSON数据'}), 400

            batch_data = data.get('batch_data', [])
            batch_id = data.get('batch_id', f'batch_{int(time.time())}')

            if not batch_data:
                return jsonify({'error': '批量数据不能为空'}), 400

            success_count = 0
            error_count = 0
            errors = []

            for i, item in enumerate(batch_data):
                try:
                    # 为每个数据项添加时间戳（如果没有的话）
                    if 'timestamp' not in item and 'time' not in item:
                        item['timestamp'] = int(time.time()) + i  # 避免时间戳冲突
                        item['datetime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    # 注入数据
                    if hasattr(self.sensor_system, 'sensor_collector'):
                        timestamp = self.sensor_system.sensor_collector.inject_test_data(item)
                        if timestamp:
                            success_count += 1
                        else:
                            error_count += 1
                            errors.append(f'项目 {i}: 注入失败')
                    else:
                        error_count += 1
                        errors.append(f'项目 {i}: 传感器收集器未初始化')

                except Exception as e:
                    error_count += 1
                    errors.append(f'项目 {i}: {str(e)}')

            return jsonify({
                'message': f'批量数据注入完成',
                'batch_id': batch_id,
                'total_items': len(batch_data),
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors[:10],  # 只返回前10个错误
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'批量注入数据失败: {str(e)}'}), 500

    def _inject_error_data(self):
        """注入错误数据"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': '请提供JSON数据'}), 400

            # 添加错误标记
            data['is_error'] = True
            data['error_type'] = data.get('error_type', 'test_error')

            # 注入错误数据
            if self.sensor_system.sensor_collector:
                result = self.sensor_system.sensor_collector.inject_test_data(data)
                if result:
                    return jsonify({
                        'message': '错误数据注入成功',
                        'injected_data': data,
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    return jsonify({'error': '错误数据注入失败'}), 500
            else:
                return jsonify({'error': '传感器收集器未初始化'}), 500

        except Exception as e:
            return jsonify({'error': f'错误数据注入失败: {str(e)}'}), 500

    def _inject_batch_data(self):
        """批量注入数据"""
        try:
            data = request.get_json()
            if not data or 'batch_data' not in data:
                return jsonify({'error': '请提供batch_data字段'}), 400

            batch_data = data['batch_data']
            if not isinstance(batch_data, list):
                return jsonify({'error': 'batch_data必须是数组'}), 400

            results = []
            success_count = 0

            for i, item in enumerate(batch_data):
                try:
                    if self.sensor_system.sensor_collector:
                        result = self.sensor_system.sensor_collector.inject_test_data(item)
                        if result:
                            success_count += 1
                            results.append({'index': i, 'status': 'success'})
                        else:
                            results.append({'index': i, 'status': 'failed', 'error': '注入失败'})
                    else:
                        results.append({'index': i, 'status': 'failed', 'error': '传感器收集器未初始化'})
                except Exception as e:
                    results.append({'index': i, 'status': 'failed', 'error': str(e)})

            return jsonify({
                'message': f'批量注入完成，成功 {success_count}/{len(batch_data)} 条',
                'success_count': success_count,
                'total_count': len(batch_data),
                'results': results,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'批量数据注入失败: {str(e)}'}), 500

    def _inject_mosquito_data(self):
        """注入蚊虫检测数据"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': '请提供JSON数据'}), 400

            # 验证必需字段
            required_fields = ['detection_id', 'timestamp', 'detections']
            for field in required_fields:
                if field not in data:
                    return jsonify({'error': f'缺少必需字段: {field}'}), 400

            # 验证detections格式
            detections = data['detections']
            if not isinstance(detections, list):
                return jsonify({'error': 'detections必须是数组'}), 400

            # 构造蚊虫检测数据格式
            mosquito_data = {
                "ver": "2.0",
                "data": {
                    "time": data['timestamp'],
                    "picurl": f"https://test.example.com/mosquito_{data['detection_id']}.jpg",
                    "lr": detections
                }
            }

            # 创建临时文件并写入数据
            import os
            import json
            import tempfile

            # 确保蚊虫检测目录存在
            mosquito_dir = "test_data/mosquito_shared/incoming"
            os.makedirs(mosquito_dir, exist_ok=True)

            # 生成文件名
            filename = f"mosquito_detection_{data['detection_id']}_{int(time.time())}.json"
            file_path = os.path.join(mosquito_dir, filename)

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(mosquito_data, f, indent=2, ensure_ascii=False)

            return jsonify({
                'message': '蚊虫检测数据注入成功',
                'detection_id': data['detection_id'],
                'file_path': file_path,
                'detections_count': len(detections),
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'蚊虫数据注入失败: {str(e)}'}), 500

    def _get_latest_sensor_data(self):
        """获取最新传感器数据"""
        try:
            # 从传感器数据文件中读取最新数据
            sensor_data_file = "test_data/sensors/current/sensor_data.json"

            if not os.path.exists(sensor_data_file):
                return jsonify({
                    'message': '暂无传感器数据',
                    'data': None,
                    'timestamp': datetime.now().isoformat()
                })

            # 读取JSON数据
            with open(sensor_data_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    return jsonify({
                        'message': '传感器数据文件为空',
                        'data': None,
                        'timestamp': datetime.now().isoformat()
                    })

                try:
                    # 尝试解析为JSON数组
                    data = json.loads(content)
                    if isinstance(data, list) and data:
                        # 如果是数组，获取最后一个元素
                        latest_data = data[-1]
                        return jsonify({
                            'message': '获取最新传感器数据成功',
                            'data': latest_data,
                            'timestamp': datetime.now().isoformat()
                        })
                    elif isinstance(data, dict):
                        # 如果是单个对象
                        return jsonify({
                            'message': '获取最新传感器数据成功',
                            'data': data,
                            'timestamp': datetime.now().isoformat()
                        })
                    else:
                        return jsonify({
                            'message': '传感器数据格式不正确',
                            'data': None,
                            'timestamp': datetime.now().isoformat()
                        })
                except json.JSONDecodeError:
                    # 如果不是标准JSON，尝试按行解析（JSONL格式）
                    lines = content.split('\n')
                    for line in reversed(lines):
                        line = line.strip()
                        if line:
                            try:
                                latest_data = json.loads(line)
                                return jsonify({
                                    'message': '获取最新传感器数据成功',
                                    'data': latest_data,
                                    'timestamp': datetime.now().isoformat()
                                })
                            except json.JSONDecodeError:
                                continue

                    return jsonify({
                        'message': '无法解析传感器数据文件',
                        'data': None,
                        'timestamp': datetime.now().isoformat()
                    })

        except Exception as e:
            return jsonify({'error': f'获取最新传感器数据失败: {str(e)}'}), 500

    def _get_sensor_history(self):
        """获取传感器历史数据"""
        try:
            limit = request.args.get('limit', 10, type=int)
            limit = min(limit, 100)  # 最多返回100条

            sensor_data_file = "test_data/sensors/current/sensor_data.json"

            if not os.path.exists(sensor_data_file):
                return jsonify({
                    'message': '暂无传感器历史数据',
                    'data': [],
                    'count': 0,
                    'timestamp': datetime.now().isoformat()
                })

            # 读取历史数据
            with open(sensor_data_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    return jsonify({
                        'message': '传感器数据文件为空',
                        'data': [],
                        'count': 0,
                        'timestamp': datetime.now().isoformat()
                    })

                history_data = []

                try:
                    # 尝试解析为JSON数组
                    data = json.loads(content)
                    if isinstance(data, list):
                        # 如果是数组，获取最后limit条数据
                        history_data = data[-limit:] if len(data) > limit else data
                    elif isinstance(data, dict):
                        # 如果是单个对象
                        history_data = [data]
                except json.JSONDecodeError:
                    # 如果不是标准JSON，尝试按行解析（JSONL格式）
                    lines = content.split('\n')
                    for line in lines[-limit:]:
                        line = line.strip()
                        if line:
                            try:
                                data = json.loads(line)
                                history_data.append(data)
                            except json.JSONDecodeError:
                                continue

                return jsonify({
                    'message': f'获取传感器历史数据成功',
                    'data': history_data,
                    'count': len(history_data),
                    'limit': limit,
                    'timestamp': datetime.now().isoformat()
                })

        except Exception as e:
            return jsonify({'error': f'获取传感器历史数据失败: {str(e)}'}), 500

    def _get_data_stats(self):
        """获取数据统计信息"""
        try:
            stats = {
                'sensor_data': {'count': 0, 'latest_time': None},
                'mqtt_messages': {'count': 0, 'latest_time': None},
                'mosquito_detections': {'processed': 0, 'pending': 0},
                'files': {
                    'sensor_data_size': 0,
                    'mqtt_messages_size': 0
                }
            }

            # 统计传感器数据
            sensor_data_file = "test_data/sensors/current/sensor_data.json"
            if os.path.exists(sensor_data_file):
                with open(sensor_data_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        try:
                            # 尝试解析为JSON数组
                            data = json.loads(content)
                            if isinstance(data, list):
                                stats['sensor_data']['count'] = len(data)
                                if data:
                                    stats['sensor_data']['latest_time'] = data[-1].get('time')
                            elif isinstance(data, dict):
                                stats['sensor_data']['count'] = 1
                                stats['sensor_data']['latest_time'] = data.get('time')
                        except json.JSONDecodeError:
                            # 如果不是标准JSON，尝试按行解析（JSONL格式）
                            lines = content.split('\n')
                            valid_lines = []
                            for line in lines:
                                line = line.strip()
                                if line:
                                    try:
                                        data = json.loads(line)
                                        valid_lines.append(data)
                                    except json.JSONDecodeError:
                                        continue

                            stats['sensor_data']['count'] = len(valid_lines)
                            if valid_lines:
                                stats['sensor_data']['latest_time'] = valid_lines[-1].get('time')

                stats['files']['sensor_data_size'] = os.path.getsize(sensor_data_file)

            # 统计MQTT消息
            mqtt_messages_file = "test_data/mock_data/mqtt_messages.json"
            if os.path.exists(mqtt_messages_file):
                with open(mqtt_messages_file, 'r', encoding='utf-8') as f:
                    try:
                        messages = json.load(f)
                        if isinstance(messages, list):
                            stats['mqtt_messages']['count'] = len(messages)
                            if messages:
                                stats['mqtt_messages']['latest_time'] = messages[-1].get('timestamp')
                    except:
                        pass

                stats['files']['mqtt_messages_size'] = os.path.getsize(mqtt_messages_file)

            # 统计蚊虫检测文件
            mosquito_incoming = "test_data/mosquito_shared/incoming"
            mosquito_processed = "test_data/mosquito_shared/processed"

            if os.path.exists(mosquito_incoming):
                stats['mosquito_detections']['pending'] = len([f for f in os.listdir(mosquito_incoming) if f.endswith('.json')])

            if os.path.exists(mosquito_processed):
                stats['mosquito_detections']['processed'] = len([f for f in os.listdir(mosquito_processed) if f.endswith('.json')])

            return jsonify({
                'message': '获取数据统计成功',
                'stats': stats,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'获取数据统计失败: {str(e)}'}), 500

    def _get_current_performance(self):
        """获取当前性能数据"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(interval=0.1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent,
                'timestamp': datetime.now().isoformat()
            }
        except Exception:
            return {'error': '无法获取性能数据'}

    def _start_performance_monitoring(self):
        """开始性能监控"""
        try:
            if self.monitoring_active:
                return jsonify({'message': '性能监控已在运行'})

            data = request.get_json() or {}
            interval = data.get('interval', 5)  # 默认5秒间隔
            duration = data.get('duration', 300)  # 默认5分钟

            self.monitoring_active = True
            self.performance_data = []

            def monitor():
                start_time = time.time()
                while self.monitoring_active and (time.time() - start_time) < duration:
                    try:
                        perf_data = {
                            'timestamp': datetime.now().isoformat(),
                            'cpu_percent': psutil.cpu_percent(interval=0.1),
                            'memory_percent': psutil.virtual_memory().percent,
                            'memory_used_mb': psutil.virtual_memory().used / 1024 / 1024,
                            'disk_usage_percent': psutil.disk_usage('/').percent,
                            'process_count': len(psutil.pids()),
                            'uptime': time.time() - getattr(self.sensor_system, 'start_time', time.time())
                        }

                        # 添加GPU信息（如果可用）
                        try:
                            import GPUtil
                            gpus = GPUtil.getGPUs()
                            if gpus:
                                gpu = gpus[0]
                                perf_data['gpu_percent'] = gpu.load * 100
                                perf_data['gpu_memory_percent'] = gpu.memoryUtil * 100
                        except ImportError:
                            pass

                        self.performance_data.append(perf_data)

                        # 限制数据量，保留最近1000条记录
                        if len(self.performance_data) > 1000:
                            self.performance_data = self.performance_data[-1000:]

                        time.sleep(interval)
                    except Exception as e:
                        print(f"性能监控错误: {e}")
                        break

                self.monitoring_active = False

            self.monitoring_thread = threading.Thread(target=monitor, daemon=True)
            self.monitoring_thread.start()

            return jsonify({
                'message': '性能监控已启动',
                'interval': interval,
                'duration': duration,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'启动性能监控失败: {str(e)}'}), 500

    def _stop_performance_monitoring(self):
        """停止性能监控"""
        try:
            if not self.monitoring_active:
                return jsonify({'message': '性能监控未在运行'})

            self.monitoring_active = False

            return jsonify({
                'message': '性能监控已停止',
                'collected_samples': len(self.performance_data),
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'停止性能监控失败: {str(e)}'}), 500

    def _get_monitoring_data(self):
        """获取监控数据"""
        try:
            limit = request.args.get('limit', type=int, default=100)
            start_time = request.args.get('start_time')
            end_time = request.args.get('end_time')

            data = self.performance_data.copy()

            # 时间过滤
            if start_time:
                data = [d for d in data if d['timestamp'] >= start_time]
            if end_time:
                data = [d for d in data if d['timestamp'] <= end_time]

            # 限制数量
            if limit > 0:
                data = data[-limit:]

            # 计算统计信息
            stats = {}
            if data:
                cpu_values = [d.get('cpu_percent', 0) for d in data]
                memory_values = [d.get('memory_percent', 0) for d in data]

                stats = {
                    'cpu': {
                        'avg': sum(cpu_values) / len(cpu_values),
                        'max': max(cpu_values),
                        'min': min(cpu_values)
                    },
                    'memory': {
                        'avg': sum(memory_values) / len(memory_values),
                        'max': max(memory_values),
                        'min': min(memory_values)
                    }
                }

            return jsonify({
                'monitoring_active': self.monitoring_active,
                'total_samples': len(self.performance_data),
                'returned_samples': len(data),
                'statistics': stats,
                'data': data,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'获取监控数据失败: {str(e)}'}), 500

    def _get_mock_mqtt_messages(self):
        """获取模拟MQTT消息"""
        try:
            if not hasattr(self.sensor_system, 'mock_mqtt_client') or not self.sensor_system.mock_mqtt_client:
                return jsonify({'error': '模拟MQTT客户端未启用'}), 400

            limit = request.args.get('limit', type=int, default=50)
            message_type = request.args.get('type')

            mock_client = self.sensor_system.mock_mqtt_client
            messages = getattr(mock_client, 'message_history', [])

            # 按类型过滤
            if message_type:
                messages = [msg for msg in messages if msg.get('type') == message_type]

            # 限制数量
            if limit > 0:
                messages = messages[-limit:]

            # 获取统计信息
            stats = getattr(mock_client, 'stats', {})

            return jsonify({
                'total_messages': len(getattr(mock_client, 'message_history', [])),
                'returned_messages': len(messages),
                'statistics': stats,
                'messages': messages,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'获取MQTT消息失败: {str(e)}'}), 500

    def _clear_mock_mqtt_messages(self):
        """清空MQTT消息历史"""
        try:
            if not hasattr(self.sensor_system, 'mock_mqtt_client') or not self.sensor_system.mock_mqtt_client:
                return jsonify({'error': '模拟MQTT客户端未启用'}), 400

            mock_client = self.sensor_system.mock_mqtt_client
            if hasattr(mock_client, 'message_history'):
                cleared_count = len(mock_client.message_history)
                mock_client.message_history.clear()
            else:
                cleared_count = 0

            # 重置统计信息
            if hasattr(mock_client, 'stats'):
                mock_client.stats = {
                    'total_published': 0,
                    'sensor_data_count': 0,
                    'error_data_count': 0,
                    'co2_status_count': 0,
                    'device_check_count': 0,
                    'mosquito_data_count': 0
                }

            return jsonify({
                'message': 'MQTT消息历史已清空',
                'cleared_count': cleared_count,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'清空MQTT消息失败: {str(e)}'}), 500

    def _simulate_network_failure(self):
        """模拟网络故障"""
        try:
            if not hasattr(self.sensor_system, 'mock_mqtt_client') or not self.sensor_system.mock_mqtt_client:
                return jsonify({'error': '模拟MQTT客户端未启用'}), 400

            data = request.get_json() or {}
            duration = data.get('duration', 30.0)  # 默认30秒
            failure_rate = data.get('failure_rate', 1.0)  # 默认100%失败率

            mock_client = self.sensor_system.mock_mqtt_client
            if hasattr(mock_client, 'simulate_network_failure'):
                mock_client.simulate_network_failure(duration, failure_rate)

                return jsonify({
                    'message': '网络故障模拟已启动',
                    'duration': duration,
                    'failure_rate': failure_rate,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return jsonify({'error': '模拟MQTT客户端不支持网络故障模拟'}), 400

        except Exception as e:
            return jsonify({'error': f'模拟网络故障失败: {str(e)}'}), 500

    def _start_test_session(self):
        """开始测试会话"""
        try:
            data = request.get_json() or {}
            session_name = data.get('name', f'test_session_{int(time.time())}')
            description = data.get('description', '')

            session_id = f"session_{int(time.time() * 1000)}"

            session_info = {
                'id': session_id,
                'name': session_name,
                'description': description,
                'start_time': datetime.now().isoformat(),
                'status': 'active',
                'injected_data_count': 0,
                'performance_samples': 0,
                'mqtt_messages': 0
            }

            self.test_sessions[session_id] = session_info
            self.current_session_id = session_id

            return jsonify({
                'message': '测试会话已启动',
                'session': session_info,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'启动测试会话失败: {str(e)}'}), 500

    def _stop_test_session(self):
        """结束测试会话"""
        try:
            if not self.current_session_id or self.current_session_id not in self.test_sessions:
                return jsonify({'error': '没有活动的测试会话'}), 400

            session = self.test_sessions[self.current_session_id]
            session['status'] = 'completed'
            session['end_time'] = datetime.now().isoformat()

            # 更新会话统计信息
            session['performance_samples'] = len(self.performance_data)
            if hasattr(self.sensor_system, 'mock_mqtt_client') and self.sensor_system.mock_mqtt_client:
                mock_client = self.sensor_system.mock_mqtt_client
                session['mqtt_messages'] = len(getattr(mock_client, 'message_history', []))

            self.current_session_id = None

            return jsonify({
                'message': '测试会话已结束',
                'session': session,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'结束测试会话失败: {str(e)}'}), 500

    def _get_test_session_status(self):
        """获取测试会话状态"""
        try:
            if self.current_session_id and self.current_session_id in self.test_sessions:
                current_session = self.test_sessions[self.current_session_id].copy()

                # 更新实时统计信息
                current_session['performance_samples'] = len(self.performance_data)
                if hasattr(self.sensor_system, 'mock_mqtt_client') and self.sensor_system.mock_mqtt_client:
                    mock_client = self.sensor_system.mock_mqtt_client
                    current_session['mqtt_messages'] = len(getattr(mock_client, 'message_history', []))
            else:
                current_session = None

            return jsonify({
                'current_session': current_session,
                'all_sessions': list(self.test_sessions.values()),
                'total_sessions': len(self.test_sessions),
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'获取测试会话状态失败: {str(e)}'}), 500

    def _get_test_env_status(self):
        """获取测试环境状态"""
        try:
            env_status = {
                'test_mode_enabled': self.sensor_system.test_mode,
                'mock_components': {
                    'mqtt_client': hasattr(self.sensor_system, 'mock_mqtt_client') and self.sensor_system.mock_mqtt_client is not None,
                    'hardware': getattr(self.sensor_system.sensor_collector, 'mock_controller', None) is not None if self.sensor_system.sensor_collector else False
                },
                'data_isolation': {
                    'test_data_dir': 'test_data' if self.sensor_system.test_mode else None,
                    'production_data_dir': 'data' if not self.sensor_system.test_mode else None
                },
                'api_server': {
                    'running': self.server is not None,
                    'monitoring_active': self.monitoring_active,
                    'current_session': self.current_session_id
                },
                'system_resources': self._get_current_performance(),
                'timestamp': datetime.now().isoformat()
            }

            return jsonify(env_status)

        except Exception as e:
            return jsonify({'error': f'获取测试环境状态失败: {str(e)}'}), 500

    def _reset_test_env(self):
        """重置测试环境"""
        try:
            reset_actions = []

            # 停止当前测试会话
            if self.current_session_id:
                self._stop_test_session()
                reset_actions.append('stopped_test_session')

            # 停止性能监控
            if self.monitoring_active:
                self._stop_performance_monitoring()
                reset_actions.append('stopped_performance_monitoring')

            # 清空性能数据
            self.performance_data.clear()
            reset_actions.append('cleared_performance_data')

            # 清空MQTT消息历史
            if hasattr(self.sensor_system, 'mock_mqtt_client') and self.sensor_system.mock_mqtt_client:
                self._clear_mock_mqtt_messages()
                reset_actions.append('cleared_mqtt_messages')

            # 重置传感器收集器状态
            if self.sensor_system.sensor_collector:
                try:
                    # 重置数据计数器
                    if hasattr(self.sensor_system.sensor_collector, 'api_data_counts'):
                        self.sensor_system.sensor_collector.api_data_counts = {
                            'total_injected': 0,
                            'sensor_data': 0,
                            'error_data': 0,
                            'batch_data': 0
                        }
                    reset_actions.append('reset_sensor_collector_counters')
                except Exception as e:
                    reset_actions.append(f'sensor_collector_reset_failed: {str(e)}')

            # 清理测试会话历史
            self.test_sessions.clear()
            reset_actions.append('cleared_test_sessions')

            return jsonify({
                'message': '测试环境重置完成',
                'reset_actions': reset_actions,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'重置测试环境失败: {str(e)}'}), 500

    def _get_test_config(self):
        """获取测试配置"""
        try:
            config = self.sensor_system.config

            # 提取测试相关配置
            test_config = {
                'test_mode': config.get('test_mode', {}),
                'test_api': config.get('test_api', {}),
                'data_storage': config.get('data_storage', {}),
                'mqtt': {
                    'enable_mqtt_connection': config.get('mqtt', {}).get('enable_mqtt_connection', True),
                    'mock_mode': config.get('mqtt', {}).get('mock_mode', False)
                },
                'sensors': {
                    sensor: sensor_config for sensor, sensor_config in config.get('sensors', {}).items()
                },
                'timestamp': datetime.now().isoformat()
            }

            return jsonify(test_config)

        except Exception as e:
            return jsonify({'error': f'获取测试配置失败: {str(e)}'}), 500

    def _update_test_config(self):
        """更新测试配置"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({'error': '请提供配置数据'}), 400

            # 注意：这里只是演示，实际生产环境中不应该允许动态修改配置
            # 这个接口主要用于测试场景配置的临时调整

            updated_fields = []

            # 更新测试API配置
            if 'test_api' in data:
                test_api_config = data['test_api']
                current_config = self.sensor_system.config.get('test_api', {})
                current_config.update(test_api_config)
                updated_fields.append('test_api')

            # 更新传感器配置（仅限测试模式）
            if 'sensors' in data and self.sensor_system.test_mode:
                sensors_config = data['sensors']
                current_sensors = self.sensor_system.config.get('sensors', {})
                for sensor, config in sensors_config.items():
                    if sensor in current_sensors:
                        current_sensors[sensor].update(config)
                        updated_fields.append(f'sensors.{sensor}')

            return jsonify({
                'message': '测试配置更新完成',
                'updated_fields': updated_fields,
                'warning': '配置更改仅在当前会话有效，重启后将恢复原始配置',
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'更新测试配置失败: {str(e)}'}), 500

    def _control_power(self):
        """电源控制"""
        try:
            data = request.get_json() or {}
            action = data.get('action')  # 'on' 或 'off'

            if action not in ['on', 'off']:
                return jsonify({'error': '无效的电源操作，支持: on, off'}), 400

            if action == 'on':
                self.sensor_system.power_on()
                return jsonify({
                    'message': '设备开机成功',
                    'power_status': self.sensor_system.power_status,
                    'timestamp': datetime.now().isoformat()
                })
            else:  # off
                self.sensor_system.power_off()
                return jsonify({
                    'message': '设备关机成功',
                    'power_status': self.sensor_system.power_status,
                    'timestamp': datetime.now().isoformat()
                })

        except Exception as e:
            return jsonify({'error': f'电源控制失败: {str(e)}'}), 500

    def _get_components_status(self):
        """获取组件状态"""
        try:
            components = {}

            # 传感器收集器
            if self.sensor_system.sensor_collector:
                components['sensor_collector'] = {
                    'running': getattr(self.sensor_system.sensor_collector, 'running', False),
                    'test_mode': getattr(self.sensor_system.sensor_collector, 'test_mode', False),
                    'mock_hardware': getattr(self.sensor_system.sensor_collector, 'mock_hardware', False),
                    'data_counts': getattr(self.sensor_system.sensor_collector, 'api_data_counts', {})
                }

            # MQTT客户端
            if self.sensor_system.mqtt_client:
                components['mqtt_client'] = {
                    'connected': getattr(self.sensor_system.mqtt_client, 'connected', False),
                    'is_mock': hasattr(self.sensor_system, 'mock_mqtt_client'),
                    'message_count': len(getattr(self.sensor_system.mock_mqtt_client, 'message_history', [])) if hasattr(self.sensor_system, 'mock_mqtt_client') else 0
                }

            # CO2控制器
            if self.sensor_system.co2_controller:
                components['co2_controller'] = {
                    'running': getattr(self.sensor_system.co2_controller, 'running', False),
                    'control_enabled': getattr(self.sensor_system.co2_controller, 'control_enabled', False)
                }

            # 设备健康监控
            if self.sensor_system.device_health:
                components['device_health'] = {
                    'running': getattr(self.sensor_system.device_health, 'running', False),
                    'check_interval': getattr(self.sensor_system.device_health, 'check_interval', 0)
                }

            return jsonify({
                'components': components,
                'total_components': len(components),
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'获取组件状态失败: {str(e)}'}), 500

    def _control_component(self, component_name):
        """控制单个组件"""
        try:
            data = request.get_json() or {}
            action = data.get('action')  # 'start' 或 'stop'

            if action not in ['start', 'stop']:
                return jsonify({'error': '无效的组件操作，支持: start, stop'}), 400

            component = None
            if component_name == 'sensor_collector':
                component = self.sensor_system.sensor_collector
            elif component_name == 'mqtt_client':
                component = self.sensor_system.mqtt_client
            elif component_name == 'co2_controller':
                component = self.sensor_system.co2_controller
            elif component_name == 'device_health':
                component = self.sensor_system.device_health
            else:
                return jsonify({'error': f'未知组件: {component_name}'}), 400

            if not component:
                return jsonify({'error': f'组件 {component_name} 未初始化'}), 400

            if action == 'start':
                if hasattr(component, 'start'):
                    component.start()
                    return jsonify({
                        'message': f'组件 {component_name} 启动成功',
                        'component': component_name,
                        'action': action,
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    return jsonify({'error': f'组件 {component_name} 不支持启动操作'}), 400
            else:  # stop
                if hasattr(component, 'stop'):
                    component.stop()
                    return jsonify({
                        'message': f'组件 {component_name} 停止成功',
                        'component': component_name,
                        'action': action,
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    return jsonify({'error': f'组件 {component_name} 不支持停止操作'}), 400

        except Exception as e:
            return jsonify({'error': f'控制组件失败: {str(e)}'}), 500

    def _get_sensors_status(self):
        """获取传感器状态"""
        try:
            if not self.sensor_system.sensor_collector:
                return jsonify({'error': '传感器收集器未初始化'}), 400

            sensor_states = getattr(self.sensor_system.sensor_collector, 'sensor_states', {})
            sensor_enabled = getattr(self.sensor_system.sensor_collector, 'sensor_enabled', {})

            sensors = {}
            for sensor_name, state in sensor_states.items():
                sensors[sensor_name] = {
                    'status': state.get('status', 'unknown'),
                    'enabled': sensor_enabled.get(sensor_name, False),
                    'last_success': state.get('last_success'),
                    'last_error': state.get('last_error'),
                    'error_message': state.get('error_message'),
                    'failure_count': state.get('failure_count', 0)
                }

            return jsonify({
                'sensors': sensors,
                'total_sensors': len(sensors),
                'test_mode': getattr(self.sensor_system.sensor_collector, 'test_mode', False),
                'mock_hardware': getattr(self.sensor_system.sensor_collector, 'mock_hardware', False),
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'获取传感器状态失败: {str(e)}'}), 500

    def _control_sensor(self, sensor_name):
        """控制单个传感器"""
        try:
            if not self.sensor_system.sensor_collector:
                return jsonify({'error': '传感器收集器未初始化'}), 400

            data = request.get_json() or {}
            action = data.get('action')  # 'enable' 或 'disable'

            if action not in ['enable', 'disable']:
                return jsonify({'error': '无效的传感器操作，支持: enable, disable'}), 400

            sensor_enabled = getattr(self.sensor_system.sensor_collector, 'sensor_enabled', {})

            # 检查传感器是否存在
            valid_sensors = ['co2', 'temp', 'hum', 'gps', 'wind_speed']
            if sensor_name not in valid_sensors:
                return jsonify({'error': f'未知传感器: {sensor_name}，支持的传感器: {valid_sensors}'}), 400

            # 更新传感器状态
            if action == 'enable':
                sensor_enabled[sensor_name] = True
                message = f'传感器 {sensor_name} 已启用'
            else:  # disable
                sensor_enabled[sensor_name] = False
                message = f'传感器 {sensor_name} 已禁用'

            return jsonify({
                'message': message,
                'sensor': sensor_name,
                'action': action,
                'enabled': sensor_enabled[sensor_name],
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            return jsonify({'error': f'控制传感器失败: {str(e)}'}), 500

    def _get_available_endpoints(self):
        """获取可用的API端点列表"""
        endpoints = []
        for rule in self.app.url_map.iter_rules():
            if rule.endpoint != 'static':
                endpoints.append({
                    'path': rule.rule,
                    'methods': list(rule.methods - {'HEAD', 'OPTIONS'})
                })
        return endpoints

    def start_server(self, host='0.0.0.0', port=8080):
        """启动测试API服务器"""
        try:
            self.server = make_server(host, port, self.app, threaded=True)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()

            print(f"🧪 测试API服务器已启动: http://{host}:{port}")
            print(f"📋 API文档: http://{host}:{port}/")
            print(f"🔍 系统信息: http://{host}:{port}/api/info")
            print(f"❤️ 健康检查: http://{host}:{port}/api/health")

            return True

        except Exception as e:
            print(f"启动测试API服务器失败: {e}")
            return False

    def stop_server(self):
        """停止测试API服务器"""
        try:
            if self.server:
                self.server.shutdown()
                print("🧪 测试API服务器已停止")
                return True
            return False
        except Exception as e:
            print(f"停止测试API服务器失败: {e}")
            return False


def create_testing_api(sensor_system):
    """创建测试API实例的工厂函数"""
    return TestingAPI(sensor_system)


if __name__ == "__main__":
    # 测试模式运行
    print("🧪 测试API服务器 - 独立运行模式")

    # 创建模拟的传感器系统
    class MockSensorSystem:
        def __init__(self):
            self.test_mode = True
            self.running = False
            self.sensor_collector = None
            self.mqtt_client = None
            self.co2_controller = None
            self.device_health = None
            self.start_time = time.time()

        def start(self):
            self.running = True
            print("模拟系统已启动")

        def stop(self):
            self.running = False
            print("模拟系统已停止")

        def restart(self):
            self.stop()
            time.sleep(1)
            self.start()
            print("模拟系统已重启")

    mock_system = MockSensorSystem()
    api = TestingAPI(mock_system)

    try:
        api.start_server(host='127.0.0.1', port=8080)
        print("按 Ctrl+C 停止服务器")

        # 保持服务器运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\n正在停止服务器...")
        api.stop_server()
        print("服务器已停止")
