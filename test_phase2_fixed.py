#!/usr/bin/env python3
"""
第二阶段测试脚本 - 修复版本
性能监控和压力测试，包含修复后的数据验证逻辑
"""

import requests
import time
import json
import random
from datetime import datetime
import threading
import concurrent.futures

# 测试配置
API_BASE_URL = "http://localhost:5000"
TEST_DURATION = 300  # 5分钟测试
CONCURRENT_REQUESTS = 5  # 并发请求数

def log_test(message):
    """测试日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")

def start_performance_monitoring():
    """启动性能监控"""
    try:
        response = requests.post(f"{API_BASE_URL}/api/monitor/start", 
                               json={
                                   "interval": 2,  # 2秒间隔
                                   "duration": TEST_DURATION + 60  # 比测试时间长一点
                               })
        if response.status_code == 200:
            log_test("✅ 性能监控已启动")
            return True
        else:
            log_test(f"❌ 启动性能监控失败: {response.text}")
            return False
    except Exception as e:
        log_test(f"❌ 启动性能监控异常: {e}")
        return False

def stop_performance_monitoring():
    """停止性能监控"""
    try:
        response = requests.post(f"{API_BASE_URL}/api/monitor/stop")
        if response.status_code == 200:
            log_test("✅ 性能监控已停止")
            return True
        else:
            log_test(f"❌ 停止性能监控失败: {response.text}")
            return False
    except Exception as e:
        log_test(f"❌ 停止性能监控异常: {e}")
        return False

def get_monitoring_data():
    """获取监控数据"""
    try:
        response = requests.get(f"{API_BASE_URL}/api/monitor/data")
        if response.status_code == 200:
            data = response.json()
            log_test(f"📊 获取到 {data.get('total_samples', 0)} 条性能监控数据")
            return data
        else:
            log_test(f"❌ 获取监控数据失败: {response.text}")
            return None
    except Exception as e:
        log_test(f"❌ 获取监控数据异常: {e}")
        return None

def generate_test_sensor_data(with_anomaly=False):
    """生成测试传感器数据"""
    base_data = {
        "temperature": random.uniform(15, 35),
        "humidity": random.uniform(30, 80),
        "co2": random.uniform(300, 1200),
        "longitude": random.uniform(113.0, 114.0),
        "latitude": random.uniform(22.0, 23.0),
        "wind_speed": random.uniform(0, 15),
        "timestamp": int(time.time()),
        "datetime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    if with_anomaly:
        # 添加异常数据
        if random.random() < 0.3:  # 30%概率温度异常
            base_data["temperature"] = {"value": random.uniform(-50, 100), "anomaly": "extreme"}
        if random.random() < 0.2:  # 20%概率湿度突变
            base_data["humidity"] = {"value": random.uniform(0, 100), "anomaly": "sudden_change"}
    
    return base_data

def inject_single_data():
    """注入单条数据"""
    try:
        data = generate_test_sensor_data(with_anomaly=True)
        response = requests.post(f"{API_BASE_URL}/api/data/inject/sensor", json=data)
        return response.status_code == 200
    except Exception as e:
        log_test(f"❌ 单条数据注入失败: {e}")
        return False

def inject_batch_data(batch_size=20):
    """注入批量数据"""
    try:
        batch_data = []
        for i in range(batch_size):
            data = generate_test_sensor_data(with_anomaly=True)
            data['timestamp'] = int(time.time()) + i  # 避免时间戳冲突
            batch_data.append(data)
        
        payload = {
            "batch_id": f"phase2_batch_{int(time.time())}",
            "batch_data": batch_data
        }
        
        response = requests.post(f"{API_BASE_URL}/api/data/inject/batch", json=payload)
        if response.status_code == 200:
            result = response.json()
            log_test(f"✅ 批量数据注入成功: {result.get('success_count', 0)}/{batch_size}")
            return True
        else:
            log_test(f"❌ 批量数据注入失败: {response.text}")
            return False
    except Exception as e:
        log_test(f"❌ 批量数据注入异常: {e}")
        return False

def concurrent_data_injection(duration=60):
    """并发数据注入测试"""
    log_test(f"🚀 开始并发数据注入测试 ({duration}秒)")
    
    success_count = 0
    error_count = 0
    start_time = time.time()
    
    def inject_worker():
        nonlocal success_count, error_count
        while time.time() - start_time < duration:
            if inject_single_data():
                success_count += 1
            else:
                error_count += 1
            time.sleep(random.uniform(0.5, 2.0))  # 随机间隔
    
    # 启动并发线程
    threads = []
    for i in range(CONCURRENT_REQUESTS):
        thread = threading.Thread(target=inject_worker)
        thread.start()
        threads.append(thread)
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    log_test(f"📊 并发测试完成: 成功 {success_count}, 失败 {error_count}")
    return success_count, error_count

def mixed_load_test(duration=120):
    """混合负载测试"""
    log_test(f"🔥 开始混合负载测试 ({duration}秒)")
    
    start_time = time.time()
    operations = []
    
    while time.time() - start_time < duration:
        # 随机选择操作类型
        operation_type = random.choice(['single', 'batch', 'mosquito'])
        
        if operation_type == 'single':
            success = inject_single_data()
            operations.append(('single', success))
        elif operation_type == 'batch':
            success = inject_batch_data(random.randint(5, 15))
            operations.append(('batch', success))
        elif operation_type == 'mosquito':
            # 注入蚊虫检测数据
            mosquito_data = {
                "time": int(time.time()),
                "picurl": f"https://test.example.com/mosquito_mixed_test_{random.randint(1, 100):03d}.jpg",
                "lr": [
                    {
                        "x": random.randint(10, 500),
                        "y": random.randint(10, 500),
                        "confidence": random.uniform(0.8, 0.99)
                    }
                ]
            }
            try:
                response = requests.post(f"{API_BASE_URL}/api/data/inject/mosquito", json=mosquito_data)
                success = response.status_code == 200
                operations.append(('mosquito', success))
            except Exception:
                operations.append(('mosquito', False))
        
        time.sleep(random.uniform(0.1, 1.0))  # 随机间隔
    
    # 统计结果
    stats = {}
    for op_type, success in operations:
        if op_type not in stats:
            stats[op_type] = {'success': 0, 'error': 0}
        if success:
            stats[op_type]['success'] += 1
        else:
            stats[op_type]['error'] += 1
    
    log_test(f"📊 混合负载测试完成:")
    for op_type, counts in stats.items():
        log_test(f"   {op_type}: 成功 {counts['success']}, 失败 {counts['error']}")
    
    return stats

def main():
    """主测试流程"""
    log_test("🎯 开始第二阶段测试 - 修复版本")
    log_test("=" * 60)
    
    # 1. 启动性能监控
    log_test("📊 第1步: 启动性能监控")
    if not start_performance_monitoring():
        log_test("❌ 性能监控启动失败，继续测试...")
    
    time.sleep(2)
    
    # 2. 单条数据注入测试
    log_test("📝 第2步: 单条数据注入测试")
    for i in range(10):
        success = inject_single_data()
        log_test(f"   数据 {i+1}: {'✅' if success else '❌'}")
        time.sleep(0.5)
    
    # 3. 批量数据注入测试
    log_test("📦 第3步: 批量数据注入测试")
    inject_batch_data(20)
    time.sleep(2)
    
    # 4. 并发数据注入测试
    log_test("🚀 第4步: 并发数据注入测试")
    concurrent_data_injection(60)
    time.sleep(2)
    
    # 5. 混合负载测试
    log_test("🔥 第5步: 混合负载测试")
    mixed_load_test(120)
    time.sleep(2)
    
    # 6. 停止性能监控并获取数据
    log_test("📊 第6步: 停止性能监控并获取数据")
    stop_performance_monitoring()
    time.sleep(1)
    
    monitoring_data = get_monitoring_data()
    if monitoring_data:
        stats = monitoring_data.get('statistics', {})
        log_test(f"📈 性能统计:")
        if 'cpu' in stats:
            log_test(f"   CPU: 平均 {stats['cpu'].get('avg', 0):.1f}%, 最大 {stats['cpu'].get('max', 0):.1f}%")
        if 'memory' in stats:
            log_test(f"   内存: 平均 {stats['memory'].get('avg', 0):.1f}%, 最大 {stats['memory'].get('max', 0):.1f}%")
    
    log_test("=" * 60)
    log_test("🎉 第二阶段测试完成！")
    log_test("请检查日志文件和数据文件以验证修复效果")

if __name__ == "__main__":
    main()
